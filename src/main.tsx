import React from 'react';
import { createRoot } from 'react-dom/client';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import './app.css';
import App from './App';

// Tworzenie instancji QueryClient z zoptymalizowaną konfiguracją
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes - dane są świeże przez 5 minut
      gcTime: 10 * 60 * 1000, // 10 minutes - dane są przechowywane w cache przez 10 minut
      retry: (failureCount, error: unknown) => {
        // Nie ponawiaj dla błędów 4xx (błędy klienta)
        if (error && typeof error === 'object' && 'status' in error) {
          const statusError = error as { status: number };
          if (statusError.status >= 400 && statusError.status < 500) {
            return false;
          }
        }
        // Maksymalnie 2 ponowne próby dla innych błędów
        return failureCount < 2;
      },
      refetchOnWindowFocus: false, // Nie odświeżaj automatycznie przy powrocie do okna
      refetchOnReconnect: true, // Odświeżaj przy ponownym połączeniu z internetem
    },
    mutations: {
      retry: 1, // Jedna ponowna próba dla mutacji
    },
  },
});

const rootElement = document.getElementById('root');
if (!rootElement) throw new Error('Failed to find the root element');

const root = createRoot(rootElement);

root.render(
  // Temporarily disable StrictMode to test double execution issue
  // <React.StrictMode>
  <QueryClientProvider client={queryClient}>
    <App />
    {/* React Query Devtools - tylko w środowisku deweloperskim */}
    {import.meta.env.DEV && <ReactQueryDevtools initialIsOpen={false} />}
  </QueryClientProvider>
  // </React.StrictMode>
);
