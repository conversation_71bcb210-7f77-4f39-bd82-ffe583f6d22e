import React, { useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import {
  activityService,
  useSessionDetails,
  useStartActivity,
} from '../../services/activityService';
import { BlendActivity } from '../../components/activities/BlendActivity';
import { useUserStore } from '../../store/userStore';
import { USER_ROLES } from '../../contants';
import { useTodaysSessions } from '../../services/sessionService';
import { Session } from '../../types/session';
import { useInterventionStore } from '../../store/interventionStore';

export const ActivityPage: React.FC = () => {
  const { activityId } = useParams<{ activityId: string }>();
  const navigate = useNavigate();
  const { user } = useUserStore();
  const { data: todaysSessions, isLoading: isLoadingSessions } = useTodaysSessions();
  const startActivityMutation = useStartActivity();
  const interventionStore = useInterventionStore();

  // Pobieramy szczegóły sesji na podstawie ID z URL
  const { data: sessionDetails, isLoading, error: _error } = useSessionDetails(activityId || '');

  // Auto-fetch interventions if not loaded
  useEffect(() => {
    if (!interventionStore.isLoaded) {
      interventionStore.fetchInterventions();
    }
  }, [interventionStore]);

  // Mapowanie sesji na aktywności - przeniesione przed warunkowymi return
  const activities = React.useMemo(() => {
    // Korzystamy z funkcji pomocniczej do spłaszczenia listy sesji
    const allSessions = todaysSessions ? activityService.flattenSessionsList(todaysSessions) : [];

    // Funkcja mapująca game_id na typ aktywności
    const mapGameIdToType = (
      gameId?: string
    ): 'blend' | 'reading' | 'listening' | 'writing' | 'speaking' => {
      // Zabezpieczenie przed undefined
      if (!gameId) return 'blend';

      if (gameId.includes('math')) return 'blend';
      if (gameId.includes('read')) return 'reading';
      if (gameId.includes('listen')) return 'listening';
      if (gameId.includes('write')) return 'writing';
      if (gameId.includes('speak')) return 'speaking';
      return 'blend';
    };

    // Funkcja mapująca session na tytuł aktywności używając intervention_id i game_id
    const mapSessionToTitle = (session: Session): string => {
      if (session.intervention_id) {
        return interventionStore.getGameNameByInterventionId(
          session.intervention_id,
          session.game_id
        );
      }
      // Fallback do game_id jeśli nie ma intervention_id
      return interventionStore.getGameNameById(session.game_id);
    };

    // Funkcja generująca opis sesji
    const getSessionDescription = (session: Session): string => {
      const duration = session.duration_seconds
        ? `${Math.floor(session.duration_seconds / 60)} min`
        : '10 min';

      if (session.status === 'planned') {
        return `${duration} / day • Do anytime`;
      } else if (session.status === 'in_progress') {
        return `${duration} / day • In progress`;
      } else {
        return `${duration} / day • Completed ${session.correct_percent ? `(${session.correct_percent}% correct)` : ''}`;
      }
    };

    return allSessions.map((session: Session) => ({
      id: session.session_id,
      type: mapGameIdToType(session.game_id),
      title: mapSessionToTitle(session),
      description: getSessionDescription(session),
      duration: session.duration_seconds ? Math.floor(session.duration_seconds / 60) : 10,
      status: session.status,
    }));
  }, [todaysSessions, interventionStore]);

  useEffect(() => {
    // Jeśli nie ma ID aktywności w URL, przekieruj na dashboard
    if (!activityId) {
      navigate('/dashboard');
    }
  }, [activityId, navigate]);

  // If loading is in progress, display spinner
  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-teal-700"></div>
      </div>
    );
  }

  // If an error occurred or no session was found, display a message
  if (_error || !sessionDetails) {
    return (
      <div className="max-w-4xl mx-auto p-6 text-center">
        <p className="text-red-500 mb-4">Failed to load activity details.</p>
        <button
          onClick={() => navigate('/dashboard')}
          className="bg-teal-700 hover:bg-teal-800 text-white py-3 px-8 rounded-md text-sm font-medium transition-colors duration-200"
        >
          RETURN TO DASHBOARD
        </button>
      </div>
    );
  }

  // Funkcja zwracająca odpowiednią ikonę dla danego typu aktywności
  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'blend':
        return (
          <div className="w-16 h-16 bg-purple-900 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-xs">
              QUIZ
              <br />
              TIME
            </span>
          </div>
        );
      case 'reading':
        return (
          <div className="w-16 h-16 bg-blue-600 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-xs">READ</span>
          </div>
        );
      case 'listening':
        return (
          <div className="w-16 h-16 bg-green-600 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-xs">LISTEN</span>
          </div>
        );
      case 'writing':
        return (
          <div className="w-16 h-16 bg-yellow-600 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-xs">WRITE</span>
          </div>
        );
      case 'speaking':
        return (
          <div className="w-16 h-16 bg-red-600 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-xs">SPEAK</span>
          </div>
        );
      default:
        return (
          <div className="w-16 h-16 bg-gray-600 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-xs">TASK</span>
          </div>
        );
    }
  };

  const handleStartActivity = async (activityId?: string) => {
    // Zabezpieczenie przed undefined ID
    if (!activityId) {
      return;
    }

    try {
      await startActivityMutation.mutateAsync(activityId);
      navigate(`/activities/${activityId}`);
    } catch {
      // Obsługa błędu - error handling without console
    }
  };

  // Hook useMemo przeniesiony na początek komponentu

  // Sprawdzamy rolę użytkownika
  const isTeacher = user?.role === USER_ROLES.TEACHER;

  // Renderujemy odpowiedni widok w zależności od roli użytkownika
  if (isTeacher) {
    // Widok dla nauczyciela - zgodny z dashboardem
    return (
      <div className="max-w-6xl mx-auto p-6">
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center">
            <button
              className="w-10 h-10 bg-teal-700 rounded-full flex items-center justify-center mr-4"
              onClick={() => navigate('/dashboard')}
            >
              <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M18 6L6 18M6 6L18 18"
                  stroke="white"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </button>
            <div>
              <h1 className="text-2xl font-semibold text-gray-800">Overview</h1>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-[#CFEBDC] rounded-lg p-6">
            <h2 className="text-lg font-medium text-gray-800">Students</h2>
            <div className="text-4xl font-semibold mt-2 mb-4">28</div>
            <button className="w-full py-2 bg-teal-700 text-white rounded-md text-sm font-medium">
              Add student
            </button>
          </div>

          <div className="bg-[#D0E2FB] rounded-lg p-6">
            <h2 className="text-lg font-medium text-gray-800">Generated reports</h2>
            <div className="text-4xl font-semibold mt-2 mb-1">86</div>
            <div className="text-sm text-gray-600 mb-3">2 reports pending</div>
            <button className="w-full py-2 bg-teal-700 text-white rounded-md text-sm font-medium">
              Create a report
            </button>
          </div>

          <div className="bg-[#F9D8D8] rounded-lg p-6">
            <h2 className="text-lg font-medium text-gray-800">Messages</h2>
            <div className="text-4xl font-semibold mt-2 mb-1">125</div>
            <div className="text-sm text-gray-600 mb-3">3 new</div>
            <button className="w-full py-2 bg-teal-700 text-white rounded-md text-sm font-medium">
              Show
            </button>
          </div>

          <div className="bg-[#DBEEF0] rounded-lg p-6">
            <h2 className="text-lg font-medium text-gray-800">Test and Quizzes</h2>
            <div className="text-4xl font-semibold mt-2 mb-4">33</div>
            <button className="w-full py-2 bg-teal-700 text-white rounded-md text-sm font-medium">
              Show
            </button>
          </div>
        </div>

        {/* Today's Exercises - dla widoku nauczyciela */}
        <div className="mb-8">
          <h2 className="text-2xl font-semibold text-gray-800 mb-6">Today&apos;s Exercises</h2>

          {isLoadingSessions ? (
            <div className="flex justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-teal-700"></div>
            </div>
          ) : activities.length === 0 ? (
            <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-6 text-center">
              <p className="text-gray-500">No activities available today.</p>
            </div>
          ) : (
            <div className="space-y-4">
              {activities.map(activity => (
                <div
                  key={activity.id}
                  className="bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden"
                >
                  <div className="flex p-6 items-center">
                    <div className="flex-shrink-0 mr-6">{getActivityIcon(activity.type)}</div>
                    <div className="flex-1">
                      <h3 className="text-lg font-bold text-gray-800">{activity.title}</h3>
                      <p className="text-sm text-gray-500">{activity.description}</p>
                    </div>
                    <button
                      onClick={() => handleStartActivity(activity.id)}
                      className="bg-teal-700 hover:bg-teal-800 text-white py-3 px-6 rounded-md text-sm font-medium transition-colors duration-200 uppercase"
                    >
                      Start Now
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        <div className="text-center mt-8">
          <button
            onClick={() => navigate('/dashboard')}
            className="bg-teal-700 hover:bg-teal-800 text-white py-3 px-8 rounded-md text-sm font-medium transition-colors duration-200"
          >
            RETURN TO DASHBOARD
          </button>
        </div>
      </div>
    );
  }

  // Dodatkowe sprawdzenie, czy activityId jest zdefiniowane
  if (!activityId) {
    return (
      <div className="max-w-6xl mx-auto p-6 text-center">
        <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-6 mb-8">
          <h2 className="text-2xl font-semibold text-red-600 mb-4">Error</h2>
          <p className="text-gray-700 mb-6">Cannot display activity - missing identifier.</p>
          <button
            onClick={() => navigate('/dashboard')}
            className="bg-teal-700 hover:bg-teal-800 text-white py-3 px-8 rounded-md text-sm font-medium transition-colors duration-200"
          >
            RETURN TO DASHBOARD
          </button>
        </div>
      </div>
    );
  }

  // If we have activityId, display BlendActivity
  return <BlendActivity />;
};
