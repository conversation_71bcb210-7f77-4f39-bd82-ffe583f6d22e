import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { authService, RegisterData } from '../../services/authService';
import { consentService, VerificationMethod } from '../../services/consentService';
import { Role, USER_ROLES, DEFAULT_ROLES } from '../../constants/auth';

interface ApiResponse {
  message: string;
  next_step?: string;
}

interface ApiErrorResponse {
  response?: {
    data?: {
      detail?:
        | string
        | Array<{
            type: string;
            loc: string[];
            msg: string;
            input: string;
            ctx: Record<string, unknown>;
          }>;
      message?: string;
    };
  };
  message?: string;
}

export const RegisterPage: React.FC = () => {
  const navigate = useNavigate();
  const [formData, setFormData] = useState<RegisterData>({
    first_name: '',
    last_name: '',
    email: '',
    password: '',
    role_id: 1,
    age: 0,
  });
  const [parentEmail, setParentEmail] = useState('');
  const [roles, setRoles] = useState<Role[]>(DEFAULT_ROLES);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [verificationStep, setVerificationStep] = useState<VerificationMethod | null>(null);
  const [verificationResponse, setVerificationResponse] = useState<{
    message: string;
    verification_url?: string;
    redirect_to?: string;
  } | null>(null);

  useEffect(() => {
    const fetchRoles = async () => {
      try {
        const rolesData = await authService.getRoles();
        if (Array.isArray(rolesData) && rolesData.length > 0) {
          setRoles(rolesData);
        }
      } catch {
        setRoles(DEFAULT_ROLES);
      }
    };

    fetchRoles();
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setLoading(true);
    setVerificationStep(null);
    setVerificationResponse(null);

    try {
      const response = (await authService.register(formData)) as ApiResponse;
      setLoading(false);

      if (response?.message === 'Registration successful.') {
        setSuccess(true);
        setTimeout(() => {
          navigate('/login');
        }, 3000);
      } else if (response?.message === 'Parental consent required for users under 13 years old.') {
        try {
          const consentResponse = await consentService.chooseMethod({
            child_email: formData.email,
            parent_email: parentEmail,
            verification_method: 'email',
          });
          setVerificationStep('email');
          setVerificationResponse(consentResponse);
          setError(
            'Parental consent required for users under 13 years old. Choose verification method:'
          );
        } catch (consentErr: unknown) {
          const error = consentErr as Error;
          setError(error.message || 'Error occurred while selecting verification method.');
        }
      }
    } catch (err: unknown) {
      const apiError = err as ApiErrorResponse;

      if (apiError.response?.data) {
        const errorData = apiError.response.data;

        if (errorData.detail === 'A user with this email already exists.') {
          setError('A user with this email already exists.');
        } else if (Array.isArray(errorData.detail) && errorData.detail.length > 0) {
          setError(errorData.detail[0].msg || 'Registration failed. Please try again.');
        } else {
          setError(errorData.message || 'Registration failed. Please try again.');
        }
      } else {
        setError(apiError.message || 'Registration failed. Please try again.');
      }

      setLoading(false);
    }
  };

  const handleVerificationMethod = async (method: VerificationMethod) => {
    setVerificationStep(method);
    setLoading(true);
    setError(null);
    setVerificationResponse(null);

    try {
      const response = await consentService.chooseMethod({
        child_email: formData.email,
        parent_email: parentEmail,
        verification_method: method,
      });

      setVerificationResponse(response);

      if (response.redirect_to) {
        if (method === 'credit_card') {
          navigate(response.redirect_to);
        } else if (method === 'pdf_upload') {
          navigate(response.redirect_to);
        }
      }
    } catch (err: unknown) {
      const error = err as Error;
      setError(error.message || 'An error occurred while selecting the verification method.');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'role_id' || name === 'age' ? parseInt(value) : value,
    }));
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Create your account
          </h2>
        </div>
        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
          <div className="rounded-md shadow-sm -space-y-px">
            <div>
              <label htmlFor="first_name" className="sr-only">
                First Name
              </label>
              <input
                id="first_name"
                name="first_name"
                type="text"
                required
                className="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-primaryBtn focus:border-primaryBtn focus:z-10 sm:text-sm"
                placeholder="First Name"
                value={formData.first_name}
                onChange={handleChange}
              />
            </div>
            <div>
              <label htmlFor="last_name" className="sr-only">
                Last Name
              </label>
              <input
                id="last_name"
                name="last_name"
                type="text"
                required
                className="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-primaryBtn focus:border-primaryBtn focus:z-10 sm:text-sm"
                placeholder="Last Name"
                value={formData.last_name}
                onChange={handleChange}
              />
            </div>
            <div>
              <label htmlFor="email" className="sr-only">
                Email address
              </label>
              <input
                id="email"
                name="email"
                type="email"
                autoComplete="email"
                required
                className="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-primaryBtn focus:border-primaryBtn focus:z-10 sm:text-sm"
                placeholder="Email address"
                value={formData.email}
                onChange={handleChange}
              />
            </div>
            <div>
              <label htmlFor="password" className="sr-only">
                Password
              </label>
              <input
                id="password"
                name="password"
                type="password"
                autoComplete="new-password"
                required
                className="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-primaryBtn focus:border-primaryBtn focus:z-10 sm:text-sm"
                placeholder="Password"
                value={formData.password}
                onChange={handleChange}
              />
            </div>
            <div>
              <label htmlFor="age" className="sr-only">
                Age
              </label>
              <input
                id="age"
                name="age"
                type="number"
                min="0"
                max="120"
                required
                className="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-primaryBtn focus:border-primaryBtn focus:z-10 sm:text-sm"
                placeholder="Age"
                value={formData.age}
                onChange={handleChange}
              />
            </div>
            <div>
              <label htmlFor="role_id" className="sr-only">
                Role
              </label>
              <select
                id="role_id"
                name="role_id"
                required
                className="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-primaryBtn focus:border-primaryBtn focus:z-10 sm:text-sm"
                value={formData.role_id}
                onChange={handleChange}
              >
                {roles.map(role => (
                  <option key={role.id} value={role.id}>
                    {role.name}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {verificationStep === null &&
            formData.role_id === USER_ROLES.STUDENT &&
            (formData.age ?? 0) < 13 && (
              <div>
                <label htmlFor="parent_email" className="sr-only">
                  Parent&apos;s Email
                </label>
                <input
                  id="parent_email"
                  name="parent_email"
                  type="email"
                  required
                  className="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-primaryBtn focus:border-primaryBtn focus:z-10 sm:text-sm"
                  placeholder="Parent's Email"
                  value={parentEmail}
                  onChange={e => setParentEmail(e.target.value)}
                />
              </div>
            )}

          {error && (
            <div className="space-y-4">
              <div className="text-red-500 text-sm text-center">{error}</div>
              {verificationStep && !verificationResponse && (
                <div className="flex flex-col items-center space-y-4">
                  <p className="text-sm text-gray-600">Choose verification method:</p>
                  <div className="flex justify-center space-x-4">
                    <button
                      type="button"
                      onClick={() => handleVerificationMethod('email')}
                      className={`px-4 py-2 rounded-md text-sm font-medium ${
                        verificationStep === 'email'
                          ? 'bg-teal-700 text-white'
                          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                      }`}
                    >
                      Email
                    </button>
                    <button
                      type="button"
                      onClick={() => handleVerificationMethod('credit_card')}
                      className={`px-4 py-2 rounded-md text-sm font-medium ${
                        verificationStep === 'credit_card'
                          ? 'bg-teal-700 text-white'
                          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                      }`}
                    >
                      Credit Card
                    </button>
                    <button
                      type="button"
                      onClick={() => handleVerificationMethod('pdf_upload')}
                      className={`px-4 py-2 rounded-md text-sm font-medium ${
                        verificationStep === 'pdf_upload'
                          ? 'bg-teal-700 text-white'
                          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                      }`}
                    >
                      PDF
                    </button>
                  </div>
                </div>
              )}
              {verificationResponse && (
                <div className="text-sm text-center">
                  <p className="text-green-600 mb-2">{verificationResponse.message}</p>
                  {verificationResponse.verification_url && (
                    <p className="text-gray-600">
                      Verification link has been sent to parent&apos;s email.
                    </p>
                  )}
                </div>
              )}
            </div>
          )}

          {success && (
            <div className="text-green-500 text-sm text-center animate-fade-in">
              Registered Successfully! Redirecting to login page...
            </div>
          )}

          <div>
            <button
              type="submit"
              disabled={loading || success || (verificationStep !== null && !verificationResponse)}
              className="font-medium py-3 px-4 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 bg-teal-700 text-white hover:bg-teal-800 focus:ring-teal-500 w-full disabled:opacity-50"
            >
              {loading ? 'Processing...' : success ? 'Registered!' : 'Register'}
            </button>
          </div>

          <div className="text-sm text-center">
            <a href="/login" className="font-medium text-primaryBtn hover:text-opacity-90">
              Already have an account? Sign in
            </a>
          </div>
        </form>
      </div>
    </div>
  );
};
