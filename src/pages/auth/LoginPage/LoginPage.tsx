import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import teacher from '../../../assets/teacher.jpg';

import { Logo } from '../../../components/Logo/Logo';
import { FloatingInput } from '../../../components/ui/FloatingInput';
import Checkbox from '../../../components/Checkbox/Checkbox.tsx';
import Button from '../../../components/Button/Button.tsx';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
const loginSchema = z.object({
  email: z.string().email('Wprowadź poprawny adres email'),
  password: z.string().min(8, 'Hasło musi mieć co najmniej 8 znaków'),
  rememberMe: z.boolean().optional(),
});

type LoginFormData = z.infer<typeof loginSchema>;

export const LoginPage: React.FC = () => {
  const { register } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      rememberMe: false,
    },
  });

  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const navigate = useNavigate();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    navigate('/dashboard');
  };

  return (
    <div className="flex min-h-screen w-full">
      <div className="hidden md:block md:w-2/3 bg-gray-100">
        <div className="relative w-full h-full">
          <div className="absolute top-8 left-8 z-10">
            <Logo />
          </div>
          <img
            src={teacher}
            alt="Teacher smiling"
            className="absolute top-1/2 -left-1/2 h-full object-cover translate-x-1/2 -translate-y-1/2"
          />
        </div>
      </div>

      <div className="w-full md:w-1/3 flex flex-col justify-center items-center p-6">
        <div className="w-full max-w-md">
          <div className="mb-8">
            <div className="text-sm font-medium text-gray-500 mb-2">LET&apos;S GET YOU STARTED</div>
            <h1 className="text-3xl font-bold text-gray-900">Welcome to YUBU</h1>
          </div>

          <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
            <div className="rounded-md shadow-sm -space-y-px">
              <FloatingInput
                id="email"
                name="email"
                type="email"
                label="Email address"
                required
                value={email}
                onChange={e => setEmail(e.target.value)}
              />
              <FloatingInput
                id="password"
                name="password"
                type="password"
                label="Password"
                required
                value={password}
                onChange={e => setPassword(e.target.value)}
              />
            </div>

            <div className="flex items-center justify-between">
              <Checkbox
                id="rememberMe"
                label="Remember me"
                registration={register('rememberMe')}
                className="text-[12.8px] leading-[22.53px] tracking-[0px] font-[400] font-['Fira_Sans'] text-[#000000]"
              />
              <div className="text-[12.8px] leading-[22.53px] tracking-[0px] font-[500] font-['Fira_Sans']">
                <a href="#" className="text-[#424242] hover:text-gray-700">
                  Forgot your password?
                </a>
              </div>
            </div>

            <Button type="submit" fullWidth>
              CONTINUE
            </Button>

            <div className="text-center mt-4">
              <p className="text-sm text-gray-600">
                New User?{' '}
                <a href="#" className="text-black font-medium underline">
                  SIGN UP HERE
                </a>
              </p>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;
