import React, { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { authService, LoginData } from '../../services/authService';
import { FloatingInput } from '../../components/ui/FloatingInput';
import { Checkbox } from '../../components/Checkbox/Checkbox';
import { Button } from '../../components/Button/Button';
import { Logo } from '../../components/Logo/Logo';
import teacher from '../../assets/teacher.jpg';

interface LocationState {
  from?: {
    pathname: string;
  };
}

export const LoginPage: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [formData, setFormData] = useState<LoginData>({
    email: '',
    password: '',
  });
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [rememberMe, setRememberMe] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setLoading(true);

    try {
      await authService.login(formData);
      const from = (location.state as LocationState)?.from?.pathname || '/dashboard';
      navigate(from, { replace: true });
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Login failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  return (
    <div className="flex min-h-screen w-full">
      <div className="hidden md:block md:w-2/3 bg-gray-100">
        <div className="relative w-full h-full">
          <div className="absolute top-8 left-8 z-10">
            <Logo />
          </div>
          <img
            src={teacher}
            alt="Teacher smiling"
            className="absolute top-1/2 -left-1/2 h-full object-cover translate-x-1/2 -translate-y-1/2"
          />
        </div>
      </div>

      <div className="w-full md:w-1/3 flex flex-col justify-center items-center p-6">
        <div className="w-full max-w-md">
          <div className="mb-8">
            <div className="text-sm font-medium text-gray-500 mb-2">LET&apos;S GET YOU STARTED</div>
            <h1 className="text-3xl font-bold text-gray-900">Welcome to YUBU</h1>
          </div>

          <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
            <div className="rounded-md shadow-sm -space-y-px">
              <FloatingInput
                id="email"
                name="email"
                type="email"
                label="Email address"
                required
                value={formData.email}
                onChange={handleChange}
              />
              <FloatingInput
                id="password"
                name="password"
                type="password"
                label="Password"
                required
                value={formData.password}
                onChange={handleChange}
              />
            </div>

            <div className="flex items-center justify-between">
              <Checkbox
                id="rememberMe"
                label="Remember me"
                checked={rememberMe}
                onChange={e => setRememberMe(e.target.checked)}
                className="text-[12.8px] leading-[22.53px] tracking-[0px] font-[400] font-fira-sans text-[#000000]"
              />
              <div className="text-[12.8px] leading-[22.53px] tracking-[0px] font-[500] font-fira-sans">
                <a href="#" className="text-[#424242] hover:text-gray-700">
                  Forgot your password?
                </a>
              </div>
            </div>

            {error && <div className="text-red-500 text-sm text-center">{error}</div>}

            <Button type="submit" fullWidth disabled={loading}>
              {loading ? 'Signing in...' : 'CONTINUE'}
            </Button>

            <div className="text-center mt-4">
              <p className="text-sm text-gray-600">
                New User?{' '}
                <a href="/register" className="text-black font-medium underline">
                  SIGN UP HERE
                </a>
              </p>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;
