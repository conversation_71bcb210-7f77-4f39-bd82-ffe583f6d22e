import React, { useState, useEffect } from 'react';
import { DocumentsList } from '../../components/documents/DocumentsList';
import { useNavigate, useParams } from 'react-router-dom';
import plusIcon from '../../assets/plus.svg';
import closeIcon from '../../assets/close-button.svg';
import { documentsApi } from '../../api/documents';
import backIcon from '../../assets/btn-back.svg';

interface Document {
  document_id: number;
  student_id: number;
  title: string | null;
  created_at: string;
}

export const DocumentsPage: React.FC = () => {
  const navigate = useNavigate();
  const [documents, setDocuments] = useState<Document[]>([]);
  const [recentDocuments, setRecentDocuments] = useState<Document[]>([]);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [documentToDelete, setDocumentToDelete] = useState<number | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const { id: studentId } = useParams<{ id: string }>();

  useEffect(() => {
    const fetchDocuments = async () => {
      try {
        const docs = await documentsApi.getDocumentsList();
        setDocuments(docs);
        setRecentDocuments(docs.slice(0, 2));
        setIsLoading(false);
      } catch {
        setError('Failed to load documents list');
        setIsLoading(false);
      }
    };

    fetchDocuments();
  }, []);

  const handleDeleteDocument = async (id: number) => {
    setDocumentToDelete(id);
    setDeleteModalOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (!documentToDelete) return;
    setDeleteModalOpen(false);
    setDocumentToDelete(null);
  };

  const handleSummarizeDocument = (id: number) => {
    if (studentId) {
      navigate(`/dashboard/student/${studentId}/documents/${id}/chat`);
    } else {
      navigate(`/dashboard/document/${id}/chat`);
    }
  };

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (!studentId) {
      setError('Student ID is required');
      return;
    }

    try {
      await documentsApi.addDocument(file, parseInt(studentId, 10));
      const updatedDocs = await documentsApi.getDocumentsList();
      setDocuments(updatedDocs);
      setRecentDocuments(updatedDocs.slice(0, 2));
      setSelectedFile(null);
      setError(null);
    } catch {
      setError('Failed to add document');
    }
  };

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (error) {
    return <div className="text-red-500">{error}</div>;
  }

  const handleBackToStudent = () => {
    if (studentId) {
      navigate(`/dashboard/student/${studentId}`);
    }
  };

  return (
    <div className="space-y-8 mt-8">
      {studentId && (
        <div className="flex items-center gap-3 mb-6">
          <button
            onClick={handleBackToStudent}
            className="flex items-center justify-center p-2 rounded-lg hover:bg-gray-100 transition-colors"
          >
            <img src={backIcon} alt="Back" className="w-5 h-5" />
          </button>
          <h1 className="text-[32px] leading-[32px] tracking-[0%] font-[400] font-['Fira_Sans'] text-[#5E6C84]">
            Documents
          </h1>
        </div>
      )}
      <section className="mb-5.5">
        <h2 className="text-[32px] leading-[32px] tracking-[0%] font-[400] font-['Fira_Sans'] text-[#5E6C84] mb-8">
          Recent Documents
        </h2>
        <div className="flex gap-[28px] overflow-x-auto pb-4">
          {recentDocuments.map(doc => (
            <div
              key={doc.document_id}
              className="bg-white rounded-lg p-4 w-[200px] flex-shrink-0 shadow-sm hover:shadow-md transition-shadow cursor-pointer"
              onClick={() => navigate(`/dashboard/document/${doc.document_id}`)}
            >
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 rounded-lg bg-gray-100 flex items-center justify-center">
                  <span className="text-lg font-medium text-gray-600">.txt</span>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-gray-900">{doc.title || 'Untitled'}</h3>
                  <p className="text-xs text-gray-500">
                    {new Date(doc.created_at).toLocaleDateString()}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </section>

      <section>
        <div className="flex gap-4 items-center mb-8">
          <h2 className="text-[24px] leading-[32px] tracking-[0%] font-[400] font-['Fira_Sans'] text-[#5E6C84]">
            Documents list
          </h2>
          <div className="w-[1px] h-[24px] bg-[#E4E4E4]" />
          <div className="flex gap-2">
            <label className="flex items-center justify-center text-[14px] text-[#1C1C1C] gap-2 py-2 rounded-lg transition-colors cursor-pointer hover:bg-gray-100">
              <img className="h-6 w-6" src={plusIcon} alt="" />
              Add document
              <input type="file" accept=".txt" className="hidden" onChange={handleFileSelect} />
            </label>
          </div>
        </div>

        <DocumentsList
          documents={documents.map(doc => ({
            id: doc.document_id.toString(),
            name: doc.title || 'Untitled',
            type: 'txt',
            size: '0 KB',
            date: new Date(doc.created_at).toLocaleDateString(),
          }))}
          onDelete={id => handleDeleteDocument(parseInt(id))}
          onSummarize={id => handleSummarizeDocument(parseInt(id))}
        />
      </section>

      {deleteModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50">
          <div className="relative bg-white p-6 rounded-lg w-full max-w-xl">
            <button className="absolute top-4 right-4" onClick={() => setDeleteModalOpen(false)}>
              <img src={closeIcon} alt="Close" className="w-[30px] h-[30px]" />
            </button>
            <h2 className="text-xl mb-4 font-bold">Confirm deletion</h2>
            <p className="mb-4">Are you sure you want to delete this document?</p>
            <div className="flex justify-end gap-2">
              <button
                className="px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg"
                onClick={() => setDeleteModalOpen(false)}
              >
                Cancel
              </button>
              <button
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700"
                onClick={handleConfirmDelete}
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
