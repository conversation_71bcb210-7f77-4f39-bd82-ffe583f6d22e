import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { documentsApi } from '../../api/documents';
import { useUserStore } from '../../store/userStore';
import { USER_ROLES } from '../../contants';

interface DocumentContent {
  document_id: number;
  student_id: number;
  report_text: string;
  created_at: string;
}

interface DocumentDetails {
  document_id: number;
  student_id: number;
  created_at: string;
  summary: string;
  title: string | null;
  age: number;
  grade: number;
  reason_for_referral: string;
  assessment: {
    psychological: string;
    speech_language: string;
    neurological: string;
    mathematics: string;
    literacy: string;
    academic: string;
    behavioral: string;
    emotional: string;
    motor: string;
    other: string;
  };
  diagnosis: string[];
  strengths: string[];
  weaknesses: string[];
  recommendations: string[];
  followup_needed: boolean;
  keywords: string[];
}

export const DocumentDetailsPage: React.FC = () => {
  const { documentId } = useParams<{ documentId: string }>();
  const navigate = useNavigate();
  const { user } = useUserStore();
  const [documentContent, setDocumentContent] = useState<DocumentContent | null>(null);
  const [documentDetails, setDocumentDetails] = useState<DocumentDetails | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchDocumentData = async () => {
      if (!documentId) {
        setError('Document not found');
        setIsLoading(false);
        return;
      }

      if (user?.role !== USER_ROLES.TEACHER) {
        setError('No permission to view document. Only teachers have access to documents.');
        setIsLoading(false);
        return;
      }

      try {
        const [content, details] = await Promise.all([
          documentsApi.getDocument(parseInt(documentId, 10)),
          documentsApi.getDocumentDetails(parseInt(documentId, 10)),
        ]);
        setDocumentContent(content);
        setDocumentDetails(details);
      } catch (err: unknown) {
        const errorMessage =
          err instanceof Error ? err.message : 'An error occurred while fetching the document';
        setError(errorMessage);
      } finally {
        setIsLoading(false);
      }
    };

    fetchDocumentData();
  }, [documentId, user]);

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (error) {
    return (
      <div className="p-4">
        <div
          className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative"
          role="alert"
        >
          <strong className="font-bold">Error: </strong>
          <span className="block sm:inline">{error}</span>
        </div>
        <button
          onClick={() => navigate('/dashboard/documents')}
          className="mt-4 px-4 py-2 bg-gray-100 text-gray-700 rounded hover:bg-gray-200"
        >
          Back to documents list
        </button>
      </div>
    );
  }

  if (!documentDetails) {
    return <div>Document not found</div>;
  }

  return (
    <div className="max-w-4xl mx-auto py-8 px-4">
      <div className="mb-8">
        <button
          onClick={() => navigate(-1)}
          className="text-gray-600 hover:text-gray-900 flex items-center gap-2"
        >
          ← Back
        </button>
      </div>

      <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
        <h1 className="text-2xl font-bold mb-4">{documentDetails.title || 'Untitled'}</h1>
        <div className="grid grid-cols-2 gap-4 text-sm text-gray-600 mb-6">
          <div>
            <span className="font-medium">Created:</span>{' '}
            {new Date(documentDetails.created_at).toLocaleDateString()}
          </div>
          <div>
            <span className="font-medium">Student age:</span> {documentDetails.age}
          </div>
          <div>
            <span className="font-medium">Grade:</span> {documentDetails.grade}
          </div>
        </div>

        <div className="prose max-w-none">
          <h2 className="text-xl font-semibold mb-2">Summary</h2>
          <p className="mb-6">{documentDetails.summary}</p>

          <h2 className="text-xl font-semibold mb-2">Reason for referral</h2>
          <p className="mb-6">{documentDetails.reason_for_referral}</p>

          <h2 className="text-xl font-semibold mb-2">Assessment</h2>
          <div className="grid grid-cols-2 gap-4 mb-6">
            {documentDetails.assessment &&
              Object.entries(documentDetails.assessment)
                .filter(([_, value]) => value && typeof value === 'string')
                .map(([key, value]) => (
                  <div key={key} className="bg-gray-50 p-3 rounded">
                    <span className="font-medium capitalize">{key.replace('_', ' ')}:</span>{' '}
                    {value as string}
                  </div>
                ))}
          </div>

          <h2 className="text-xl font-semibold mb-2">Diagnosis</h2>
          <ul className="list-disc pl-5 mb-6">
            {Array.isArray(documentDetails.diagnosis) &&
              documentDetails.diagnosis.map((item: string, index: number) => (
                <li key={index}>{item}</li>
              ))}
          </ul>

          <div className="grid grid-cols-2 gap-8 mb-6">
            <div>
              <h2 className="text-xl font-semibold mb-2">Strengths</h2>
              <ul className="list-disc pl-5">
                {Array.isArray(documentDetails.strengths) &&
                  documentDetails.strengths.map((item: string, index: number) => (
                    <li key={index}>{item}</li>
                  ))}
              </ul>
            </div>
            <div>
              <h2 className="text-xl font-semibold mb-2">Areas for improvement</h2>
              <ul className="list-disc pl-5">
                {Array.isArray(documentDetails.weaknesses) &&
                  documentDetails.weaknesses.map((item: string, index: number) => (
                    <li key={index}>{item}</li>
                  ))}
              </ul>
            </div>
          </div>

          <h2 className="text-xl font-semibold mb-2">Recommendations</h2>
          <ul className="list-disc pl-5 mb-6">
            {Array.isArray(documentDetails.recommendations) &&
              documentDetails.recommendations.map((item: string, index: number) => (
                <li key={index}>{item}</li>
              ))}
          </ul>

          <h2 className="text-xl font-semibold mb-2">Full report content</h2>
          <div className="bg-gray-50 p-4 rounded whitespace-pre-wrap font-mono text-sm">
            {documentContent?.report_text || 'Document content not available'}
          </div>
        </div>
      </div>
    </div>
  );
};
