import React from 'react';

export const MyProgressPage: React.FC = () => {
  const progressData = [
    { week: 'Tydzień 1', score: 65 },
    { week: 'Tydzień 2', score: 70 },
    { week: 'Tydzień 3', score: 75 },
    { week: 'Tydzień 4', score: 72 },
    { week: 'Tydzień 5', score: 80 },
    { week: 'Tydzień 6', score: 85 },
  ];

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-semibold">Mój postęp</h1>

      <div className="bg-white rounded-xl p-6 shadow">
        <h2 className="text-xl font-semibold mb-4">Postęp w nauce</h2>
        <div className="h-64">
          <div className="flex h-full items-end">
            {progressData.map((item, index) => (
              <div key={index} className="flex flex-col items-center flex-1">
                <div
                  className="w-16 bg-teal-500 rounded-t-md"
                  style={{ height: `${item.score}%` }}
                ></div>
                <div className="mt-2 text-sm">{item.week}</div>
                <div className="text-xs font-medium">{item.score}%</div>
              </div>
            ))}
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-white rounded-xl p-6 shadow">
          <h2 className="text-xl font-semibold mb-4">Umiejętności</h2>
          <div className="space-y-4">
            {[
              { skill: 'Słuchanie', progress: 76 },
              { skill: 'Czytanie', progress: 67 },
              { skill: 'Płynność', progress: 70 },
              { skill: 'Koncentracja', progress: 24 },
            ].map((skill, index) => (
              <div key={index} className="space-y-1">
                <div className="flex justify-between">
                  <span>{skill.skill}</span>
                  <span>{skill.progress}%</span>
                </div>
                <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
                  <div
                    className="h-full bg-teal-500 rounded-full"
                    style={{ width: `${skill.progress}%` }}
                  ></div>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow">
          <h2 className="text-xl font-semibold mb-4">Ostatnie osiągnięcia</h2>
          <div className="space-y-4">
            {[
              { title: 'Ukończono 5 ćwiczeń z rzędu', date: '2 dni temu', icon: '🏆' },
              { title: 'Osiągnięto 70% w czytaniu', date: '5 dni temu', icon: '📚' },
              { title: 'Ukończono wszystkie zadania tygodnia', date: '1 tydzień temu', icon: '✅' },
              { title: 'Pierwszy perfekcyjny wynik', date: '2 tygodnie temu', icon: '🌟' },
            ].map((achievement, index) => (
              <div key={index} className="flex items-center p-3 border rounded-lg">
                <div className="text-2xl mr-3">{achievement.icon}</div>
                <div>
                  <div className="font-medium">{achievement.title}</div>
                  <div className="text-sm text-gray-500">{achievement.date}</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};
