import React, { useState, useEffect } from 'react';
import { Message } from '../types';
import GameInterventionsComponent from '../../../components/games/GameInterventionsComponent';
import InterventionsFromIdsComponent from '../../../components/interventions/InterventionsFromIdsComponent';

interface ChatMessageProps {
  message: Message;
}

export const ChatMessage: React.FC<ChatMessageProps> = ({ message }) => {
  const [dots, setDots] = useState('.');

  // Efekt dla animacji wielokropka
  useEffect(() => {
    if (message.isStreaming) {
      const interval = setInterval(() => {
        setDots(prev => {
          if (prev === '.') return '..';
          if (prev === '..') return '...';
          return '.';
        });
      }, 500);

      return () => clearInterval(interval);
    }
  }, [message.isStreaming]);

  return (
    <div className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'} mb-4`}>
      {message.sender === 'system' && (
        <div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center mr-2">
          <span className="text-sm font-semibold">AI</span>
        </div>
      )}

      <div
        className={`max-w-[70%] rounded-lg p-3 ${
          message.sender === 'user'
            ? 'bg-blue-50 text-black border-2 border-primaryBtn'
            : 'bg-gray-100 text-gray-900 border border-gray-200'
        }`}
      >
        <div className="whitespace-pre-wrap">
          {message.text ? message.text : message.isStreaming ? dots : ''}
        </div>

        {/* Wyświetlanie komponentu InterventionsFromIdsComponent, gdy wiadomość zawiera interwencje */}
        {message.data &&
          typeof message.data === 'object' &&
          'interventions' in message.data &&
          Array.isArray(message.data.interventions) &&
          message.data.interventions.length > 0 && (
            <div className="mt-4">
              <InterventionsFromIdsComponent
                interventions={message.data.interventions}
                gradeLevel="1st grade"
              />
            </div>
          )}

        {/* Wyświetlanie komponentu GameInterventionsComponent, gdy wiadomość zawiera dane gier (stary format) */}
        {message.data &&
          typeof message.data === 'object' &&
          'games' in message.data &&
          Array.isArray(message.data.games) &&
          message.data.games.length > 0 && (
            <div className="mt-4">
              <GameInterventionsComponent
                areaId={(() => {
                  const sanitized = String(message.data.games[0].domain || '')
                    .toLowerCase()
                    .replace(/\s+/g, '-')
                    .replace(/[&]/g, 'and')
                    .replace(/[^a-z0-9-]/g, '');
                  return sanitized || 'default-area';
                })()}
                areaName={String(message.data.games[0].domain || 'Default area')}
                gradeLevel="1st grade"
              />
            </div>
          )}
      </div>

      {message.sender === 'user' && (
        <div className="w-8 h-8 rounded-full bg-primaryBtn flex items-center justify-center ml-2">
          <span className="text-sm font-semibold text-white">U</span>
        </div>
      )}
    </div>
  );
};

export default ChatMessage;
