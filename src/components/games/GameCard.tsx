import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Game } from '../../services/gameService';
import GameStatsSummary from './GameStatsSummary';
import GameTags from './GameTags';
import progressIcon from '../../assets/progressGames.svg';
import { useGameStatsStore } from '../../store/gameStatsStore';
import logger from '../../utils/logger';

interface GameCardProps {
  game: Game;
  studentId: string;
  onClick?: () => void;
}

export const GameCard: React.FC<GameCardProps> = ({ game, studentId, onClick }) => {
  const navigate = useNavigate();

  const handleGameSelect = () => {
    if (onClick) {
      onClick();
    } else {
      navigate(`/dashboard/interventions/${game.id}`);
    }
  };

  const latestStats = useGameStatsStore(state => state.getLatestGameStats(game.id, studentId));

  // Logging removed for production

  let gameStatus = null;

  if (latestStats) {
    if (
      latestStats.completionPercentage === 100 ||
      (game.id === 'math-game' && latestStats.completionPercentage >= 83)
    ) {
      gameStatus = 'completed';
    } else if (latestStats.completionPercentage < 100) {
      gameStatus = 'in-progress';
    }
  }

  return (
    <div
      className="bg-white rounded-xl shadow-md hover:shadow-lg transition-all duration-300 cursor-pointer w-full max-w-xs mx-auto flex flex-col"
      onClick={handleGameSelect}
    >
      {gameStatus === 'completed' && (
        <div className="bg-green-500 text-white py-2 px-4 rounded-t-xl flex items-center">
          <svg
            className="w-5 h-5 mr-2"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M9,16.17 L4.83,12 L3.41,13.41 L9,19 L21,7 L19.59,5.59 L9,16.17 Z"
              fill="white"
            />
          </svg>
          <span className="font-medium">Completed</span>
        </div>
      )}
      {gameStatus === 'in-progress' && (
        <div className="bg-blue-500 text-white py-2 px-4 rounded-t-xl flex items-center">
          <img src={progressIcon} alt="In Progress" className="w-5 h-5 mr-2" />
          <span className="font-medium">In Progress</span>
        </div>
      )}

      <div className="w-full relative">
        <img src={game.thumbnail} alt={game.name} className="w-full object-cover h-48" />
      </div>

      <div className="p-4 flex-1 flex flex-col">
        <h3 className="text-xl font-semibold text-gray-900 mb-2">{game.name}</h3>
        <p className="text-sm text-gray-600 mb-3 line-clamp-2">{game.description}</p>

        {/* Tagi gry */}
        <GameTags game={game} className="mb-4" maxTags={3} />

        <GameStatsSummary gameId={game.id} studentId={studentId} />
      </div>

      <div className="p-4 border-t border-gray-100 bg-gray-50 rounded-b-xl flex justify-center">
        <button
          onClick={e => {
            e.stopPropagation();
            handleGameSelect();
          }}
          className="w-[214px] h-[56px] rounded-[16px] p-[16px] bg-blue-600 text-white hover:bg-blue-700 transition-colors font-medium cursor-pointer flex items-center justify-center gap-[12px]"
        >
          Settings
        </button>
      </div>
    </div>
  );
};

export default GameCard;
