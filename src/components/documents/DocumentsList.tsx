import React from 'react';
import { Link } from 'react-router-dom';
import { Popover } from '@headlessui/react';
import moreIcon from '../../assets/dots.svg';

interface Document {
  id: string;
  name: string;
  type: string;
  size: string;
  date: string;
}

interface DocumentsListProps {
  documents: Document[];
  onDelete?: (id: string) => Promise<void>;
  onSummarize?: (id: string) => void;
}

export const DocumentsList: React.FC<DocumentsListProps> = ({
  documents,
  onDelete,
  onSummarize,
}) => {
  const handleOptionClick = async (documentId: string, action: 'summarize' | 'delete' | 'view') => {
    if (action === 'delete' && onDelete) {
      await onDelete(documentId);
    } else if (action === 'summarize' && onSummarize) {
      onSummarize(documentId);
    } else if (action === 'view') {
      window.location.href = `/dashboard/document/${documentId}`;
    }
  };

  const getFileIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case 'pdf':
        return '📄';
      case 'doc':
      case 'docx':
        return '📝';
      case 'xlsx':
        return '📊';
      case 'pptx':
        return '📑';
      default:
        return '📁';
    }
  };

  return (
    <div className="space-y-4 mt-6">
      <div className="grid grid-cols-1 gap-2">
        {documents.map(doc => (
          <div
            key={doc.id}
            className="bg-[#F9F9F9] rounded-lg px-5 py-4 flex items-center gap-[23px] hover:shadow-md transition-shadow relative"
          >
            <div className="flex items-center gap-[23px]">
              <div className="w-12 h-12 rounded-[9.6px] bg-gray-100 flex items-center justify-center text-2xl">
                {getFileIcon(doc.type)}
              </div>
              <div>
                <h3 className="text-base font-medium text-gray-900">{doc.name}</h3>
                <div className="flex gap-2 text-sm text-gray-500">
                  <span>{doc.size}</span>
                  <span>•</span>
                  <span>{doc.date}</span>
                  <span>•</span>
                  <span>.{doc.type}</span>
                </div>
              </div>
            </div>
            <div className="flex items-center gap-2 ml-auto">
              <Link
                to={`/dashboard/document/${doc.id}`}
                className="px-4 py-2 rounded-full text-gray-700 bg-white hover:bg-gray-100 flex items-center gap-2"
              >
                <span>Preview</span>
              </Link>
              <Popover className="relative">
                {({ open }) => (
                  <>
                    <Popover.Button className="w-10 h-10 flex items-center justify-center text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 focus:outline-none">
                      <img src={moreIcon} alt="More options" className="w-5 h-5" />
                    </Popover.Button>

                    <Popover.Panel className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg py-1 z-10 focus:outline-none">
                      <button
                        onClick={() => handleOptionClick(doc.id, 'summarize')}
                        className="w-full px-4 py-2 text-left text-sm text-gray-700 border-l-2 border-l-transparent hover:border-l-blue-500 focus:outline-none transition-colors"
                      >
                        Chat with Document
                      </button>
                      <button
                        onClick={() => handleOptionClick(doc.id, 'delete')}
                        className="w-full px-4 py-2 text-left text-sm text-red-600 border-l-2 border-l-transparent hover:border-l-red-500 focus:outline-none transition-colors"
                      >
                        Delete
                      </button>
                      <button
                        onClick={() => handleOptionClick(doc.id, 'view')}
                        className="w-full px-4 py-2 text-left text-sm text-gray-700 border-l-2 border-l-transparent hover:border-l-blue-500 focus:outline-none transition-colors"
                      >
                        Preview
                      </button>
                    </Popover.Panel>
                  </>
                )}
              </Popover>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
