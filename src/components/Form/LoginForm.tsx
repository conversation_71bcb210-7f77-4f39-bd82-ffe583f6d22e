import React from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';

const loginSchema = z.object({
  email: z.string().email('Wprowadź poprawny adres email'),
  password: z.string().min(8, 'Hasło musi mieć co najmniej 8 znaków'),
  rememberMe: z.boolean().optional(),
});

type LoginFormData = z.infer<typeof loginSchema>;

const LoginPage: React.FC = () => {
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      rememberMe: false,
    },
  });

  const onSubmit = () => {};

  return (
    <div className="flex min-h-screen w-full">
      <div className="hidden md:block md:w-1/2 bg-gray-100">
        <div className="relative w-full h-full">
          <div className="absolute top-8 left-8">
            <div className="flex items-center">
              <div className="text-2xl font-bold bg-gradient-to-r from-purple-500 via-pink-500 to-yellow-500 text-transparent bg-clip-text">
                YUBU
              </div>
              <div className="text-xs ml-2 text-gray-600">You Be You</div>
            </div>
          </div>
        </div>
      </div>

      <div className="w-full md:w-1/2 flex flex-col justify-center items-center p-6">
        <div className="w-full max-w-md">
          <div className="mb-8">
            <div className="text-sm font-medium text-gray-500 mb-2">LET&apos;S GET YOU STARTED</div>
            <h1 className="text-3xl font-bold text-gray-900">Welcome to YUBU</h1>
          </div>

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            <div>
              <label
                htmlFor="email"
                className="block text-sm font-medium text-gray-700 uppercase mb-1"
              >
                Emails
              </label>
              <input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                className={`w-full px-4 py-3 rounded-md bg-gray-100 border ${errors.email ? 'border-red-500' : 'border-gray-200'}`}
                {...register('email')}
              />
              {errors.email && <p className="mt-1 text-sm text-red-600">{errors.email.message}</p>}
            </div>

            <div>
              <label
                htmlFor="password"
                className="block text-sm font-medium text-gray-700 uppercase mb-1"
              >
                Password
              </label>
              <input
                id="password"
                type="password"
                placeholder="••••••••••••••••••"
                className={`w-full px-4 py-3 rounded-md bg-gray-100 border ${errors.password ? 'border-red-500' : 'border-gray-200'}`}
                {...register('password')}
              />
              {errors.password && (
                <p className="mt-1 text-sm text-red-600">{errors.password.message}</p>
              )}
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <input
                  id="rememberMe"
                  type="checkbox"
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  {...register('rememberMe')}
                />
                <label htmlFor="rememberMe" className="ml-2 block text-sm text-gray-700">
                  Remember me
                </label>
              </div>
              <div className="text-sm">
                <a href="#" className="text-blue-600 hover:text-blue-700">
                  Forgot Password?
                </a>
              </div>
            </div>

            <button
              type="submit"
              className="w-full bg-teal-700 text-white font-medium py-3 px-4 rounded-md hover:bg-teal-800 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2"
            >
              CONTINUE
            </button>

            <div className="text-center mt-4">
              <p className="text-sm text-gray-600">
                New User?{' '}
                <a href="#" className="text-black font-medium underline">
                  SIGN UP HERE
                </a>
              </p>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;
