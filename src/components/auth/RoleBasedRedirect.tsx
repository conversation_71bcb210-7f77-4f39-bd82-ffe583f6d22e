import React, { useEffect } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useUserStore } from '../../store/userStore';
import { USER_ROLES } from '../../contants';

interface RoleBasedRedirectProps {
  children: React.ReactNode;
}

export const RoleBasedRedirect: React.FC<RoleBasedRedirectProps> = ({ children }) => {
  const location = useLocation();
  const { user } = useUserStore();

  if (!user) {
    return <div>Ładowanie...</div>;
  }

  // Przekierowanie w zależności od roli użytkownika
  if (user.role === USER_ROLES.TEACHER) {
    // Jeśli użytkownik jest nauczycielem i próbuje uzyskać dostęp do stron studenta,
    // przekieruj go do widoku głównego dashboardu
    if (
      location.pathname === '/dashboard/my-progress' ||
      location.pathname === '/dashboard/tasks'
    ) {
      return <Navigate to="/dashboard" replace />;
    }
  }

  return <>{children}</>;
};
