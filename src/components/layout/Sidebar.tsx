import React from 'react';
import { DropdownMenuItem } from './sidebar/DropdownMenuItem';
import { MenuItem } from './sidebar/MenuItem';
import Logo from '../Logo/Logo.tsx';
import { useUserStore } from '../../store/userStore';
import { USER_ROLES } from '../../contants';
import { getRoleBasedText } from '../../utils/roleBasedText';

interface SidebarProps {
  isCollapsed: boolean;
}

export const Sidebar: React.FC<SidebarProps> = ({ isCollapsed }) => {
  const { user } = useUserStore();
  const userRole = user?.role || '';

  // Ikony SVG zgodne z projektem Figma
  const homeIcon = (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M3 9L12 2L21 9V20C21 20.5304 20.7893 21.0391 20.4142 21.4142C20.0391 21.7893 19.5304 22 19 22H5C4.46957 22 3.96086 21.7893 3.58579 21.4142C3.21071 21.0391 3 20.5304 3 20V9Z"
        stroke="#5D5D5D"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M9 22V12H15V22"
        stroke="#5D5D5D"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );

  // Ikony zaimplementowane jako komponenty inline SVG
  const tasksIcon = (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M8 6H16M8 12H16M8 18H12"
        stroke="#5D5D5D"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );

  const progressIcon = (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M16 8V16M12 11V16M8 14V16"
        stroke="#5D5D5D"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <rect x="3" y="4" width="18" height="16" rx="2" stroke="#5D5D5D" strokeWidth="2" />
    </svg>
  );

  const helpIcon = (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <circle cx="12" cy="12" r="9" stroke="#5D5D5D" strokeWidth="2" />
      <path
        d="M12 16V15.5C12 14.3954 12.8954 13.5 14 13.5C15.1046 13.5 16 12.6046 16 11.5V11C16 9.89543 15.1046 9 14 9H12C10.8954 9 10 9.89543 10 11"
        stroke="#5D5D5D"
        strokeWidth="2"
        strokeLinecap="round"
      />
      <circle cx="12" cy="17" r="1" fill="#5D5D5D" />
    </svg>
  );

  const logoutIcon = (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M16 17L21 12L16 7"
        stroke="#5D5D5D"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M21 12H9"
        stroke="#5D5D5D"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M9 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H9"
        stroke="#5D5D5D"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );

  const studentsIcon = (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M17 21V19C17 17.9391 16.5786 16.9217 15.8284 16.1716C15.0783 15.4214 14.0609 15 13 15H5C3.93913 15 2.92172 15.4214 2.17157 16.1716C1.42143 16.9217 1 17.9391 1 19V21"
        stroke="#5D5D5D"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M9 11C11.2091 11 13 9.20914 13 7C13 4.79086 11.2091 3 9 3C6.79086 3 5 4.79086 5 7C5 9.20914 6.79086 11 9 11Z"
        stroke="#5D5D5D"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M23 21V19C22.9993 18.1137 22.7044 17.2528 22.1614 16.5523C21.6184 15.8519 20.8581 15.3516 20 15.13"
        stroke="#5D5D5D"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M16 3.13C16.8604 3.35031 17.623 3.85071 18.1676 4.55232C18.7122 5.25392 19.0078 6.11683 19.0078 7.005C19.0078 7.89318 18.7122 8.75608 18.1676 9.45769C17.623 10.1593 16.8604 10.6597 16 10.88"
        stroke="#5D5D5D"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );

  const documentsIcon = (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z"
        stroke="#5D5D5D"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M14 2V8H20"
        stroke="#5D5D5D"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );

  // Menu dla nauczyciela
  const teacherOverviewSubmenu = [
    { path: '/dashboard', label: getRoleBasedText('menu.overview', userRole) },
    { path: '/dashboard/students', label: getRoleBasedText('menu.students', userRole) },
    { path: '/dashboard/interventions', label: getRoleBasedText('menu.interventions', userRole) },
    { path: '/dashboard/tests', label: 'Tests' },
  ];

  const teacherDashboardsItems = [
    {
      title: 'Dashboards',
      items: [
        {
          path: '/dashboard',
          icon: homeIcon,
          label: getRoleBasedText('menu.overview', userRole),
          hasSubmenu: true,
          submenu: teacherOverviewSubmenu,
        },
      ],
    },
  ];

  const teacherPagesItems = [
    {
      title: 'Pages',
      items: [
        {
          path: '/dashboard/students-list',
          icon: studentsIcon,
          label: getRoleBasedText('menu.students', userRole),
        },
      ],
    },
  ];

  // Menu dla studenta
  const studentDashboardsItems = [
    {
      title: '',
      items: [
        {
          path: '/dashboard',
          icon: homeIcon,
          label: 'Home',
          hasSubmenu: false,
          submenu: [],
        },
      ],
    },
  ];

  const studentPagesItems = [
    {
      title: '',
      items: [
        { path: '/dashboard/tasks', icon: tasksIcon, label: "Today's Tasks" },
        { path: '/dashboard/my-progress', icon: progressIcon, label: 'My Progress' },
      ],
    },
  ];

  // Wspólne elementy menu dla wszystkich ról
  const bottomMenuItems = [
    { path: '/dashboard/help', icon: helpIcon, label: 'Help' },
    { path: '/login', icon: logoutIcon, label: 'Log out' },
  ];

  // Wybór odpowiedniego menu w zależności od roli
  const dashboardsItems =
    userRole === USER_ROLES.STUDENT ? studentDashboardsItems : teacherDashboardsItems;
  const pagesItems = userRole === USER_ROLES.STUDENT ? studentPagesItems : teacherPagesItems;

  return (
    <div
      className={`${isCollapsed ? 'w-20' : 'w-[240px]'} h-screen bg-white border-r border-gray-100 flex flex-col font-normal transition-all duration-300`}
    >
      <div
        className={`mt-6 mb-12 px-6 w-full flex items-center ${isCollapsed ? 'justify-center' : 'justify-start'} transition-transform duration-300`}
      >
        <Logo />
      </div>

      <nav className="flex-1 px-6">
        {dashboardsItems.map(section => (
          <div key={section.title} className="mb-6">
            {section.title && (
              <h3 className="text-xs font-semibold text-gray-400 uppercase mb-3">
                {section.title}
              </h3>
            )}
            <ul className="space-y-4">
              {section.items.map(item => (
                <li key={item.path}>
                  {item.hasSubmenu && !isCollapsed ? (
                    <DropdownMenuItem
                      icon={item.icon}
                      label={item.label}
                      submenuItems={item.submenu.map(
                        (subItem: { label: string; path: string }) => ({
                          label: subItem.label,
                          path: subItem.path,
                        })
                      )}
                    />
                  ) : (
                    <MenuItem
                      icon={item.icon}
                      label={item.label}
                      path={item.path}
                      isCollapsed={isCollapsed}
                    />
                  )}
                </li>
              ))}
            </ul>
          </div>
        ))}
        {pagesItems.map(section => (
          <div key={section.title} className="mb-10">
            {section.title && (
              <h3 className="text-xs font-semibold text-gray-400 uppercase mb-3">
                {section.title}
              </h3>
            )}
            <ul className="space-y-4">
              {section.items.map(item => (
                <li key={item.path}>
                  <MenuItem
                    icon={item.icon}
                    label={item.label}
                    path={item.path}
                    isCollapsed={isCollapsed}
                  />
                </li>
              ))}
            </ul>
          </div>
        ))}
      </nav>

      <div className="mt-auto px-6 mb-6">
        <ul className="space-y-4">
          {bottomMenuItems.map(item => (
            <li key={item.path}>
              <MenuItem
                icon={item.icon}
                label={item.label}
                path={item.path}
                isCollapsed={isCollapsed}
              />
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
};
