/* eslint-disable no-console */
import React from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { authService } from '../../../services/authService';
import { clearAllStores } from '../../../utils/storeUtils';

interface MenuItemProps {
  icon: string | React.ReactNode;
  label: string;
  path: string;
  isCollapsed: boolean;
}

export const MenuItem: React.FC<MenuItemProps> = ({ icon, label, path, isCollapsed }) => {
  const navigate = useNavigate();

  const handleClick = async (e: React.MouseEvent) => {
    if (path === '/login') {
      e.preventDefault();
      try {
        await authService.logout();
        clearAllStores();
        navigate('/login');
      } catch (err) {
        console.error('Error during logout:', err);
      }
    }
  };

  return (
    <Link
      to={path}
      onClick={handleClick}
      className="flex items-center gap-3 px-3 py-2 text-gray-600 hover:bg-gray-50 rounded-md transition-colors"
    >
      <div className="w-6 h-6 flex items-center justify-center">
        {typeof icon === 'string' ? (
          <img src={icon} alt={label} className="w-5 h-5 opacity-70" />
        ) : (
          icon
        )}
      </div>
      {!isCollapsed && <span className="text-sm font-medium">{label}</span>}
    </Link>
  );
};
