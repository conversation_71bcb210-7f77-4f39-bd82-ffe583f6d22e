import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { Clock, CheckCircle, Lock } from 'lucide-react';
import testAssignmentService, { AssessmentSession } from '../../services/testAssignmentService';

interface TestsGridProps {
  studentId: number;
}

interface TestCardProps {
  session: AssessmentSession;
  onClick: () => void;
}

const TestCard: React.FC<TestCardProps> = ({ session, onClick }) => {
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'in_progress':
        return <Clock className="w-5 h-5 text-blue-500" />;
      case 'planned':
        return <Clock className="w-5 h-5 text-yellow-500" />;
      default:
        return <Lock className="w-5 h-5 text-gray-400" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return 'Completed';
      case 'in_progress':
        return 'In Progress';
      case 'planned':
        return 'Planned';
      default:
        return 'Locked';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'in_progress':
        return 'bg-blue-100 text-blue-800';
      case 'planned':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatTitle = (assessmentId: string) => {
    return assessmentId
      .replace(/-/g, ' ')
      .replace(/_/g, ' ')
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <div
      onClick={onClick}
      className="bg-white rounded-lg border border-gray-200 shadow-sm p-6 hover:shadow-md transition-shadow cursor-pointer"
    >
      <div className="flex items-start justify-between mb-4">
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-gray-900 mb-1">
            {formatTitle(session.assessment_id)}
          </h3>
          <p className="text-sm text-gray-600">Scheduled for {formatDate(session.date)}</p>
        </div>
        <div className="flex items-center space-x-2">{getStatusIcon(session.status)}</div>
      </div>

      <div className="flex items-center justify-between">
        <span
          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(session.status)}`}
        >
          {getStatusText(session.status)}
        </span>
        <span className="text-sm text-gray-500">
          Session ID: {session.session_id.slice(0, 8)}...
        </span>
      </div>
    </div>
  );
};

const TestsGrid: React.FC<TestsGridProps> = ({ studentId }) => {
  const {
    data: sessionsData,
    isLoading,
    error,
  } = useQuery({
    queryKey: ['studentSessions', studentId],
    queryFn: () => testAssignmentService.getStudentSessions(studentId),
    enabled: !!studentId,
  });

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-red-600">Error loading tests: {(error as Error).message}</p>
      </div>
    );
  }

  const sessions = sessionsData?.sessions || [];

  if (sessions.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">No tests assigned to this student yet.</p>
      </div>
    );
  }

  const handleTestClick = (session: AssessmentSession) => {
    // Navigate to test details/execution page
    const testUrl = `/dashboard/tests/${session.assessment_id}?session_id=${session.session_id}`;
    window.location.href = testUrl;
  };

  return (
    <div className="grid gap-6 grid-cols-1 md:grid-cols-2 xl:grid-cols-3">
      {sessions.map(session => (
        <TestCard
          key={session.session_id}
          session={session}
          onClick={() => handleTestClick(session)}
        />
      ))}
    </div>
  );
};

export default TestsGrid;
