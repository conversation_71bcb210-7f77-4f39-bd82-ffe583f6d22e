import React, { useState } from 'react';
import { Modal } from './Modal';
import detailSettingsIcon from '../../assets/detailSettings.svg';
import interventionService from '../../services/interventionService';
import { toast } from 'sonner';
import logger from '../../utils/logger';

import { GameType } from '../../services/gameService';

type TabType = 'frequency' | 'details' | 'promptAi';

interface GameSettingsModalProps {
  isOpen: boolean;
  onClose: () => void;
  gameType: GameType;
  gameTitle: string;
  onSave?: (settings: GameSettings) => void;
  studentId?: number;
  isTestsPage?: boolean; // determines if assigning test
}

export interface GameSettings {
  gameType: GameType;
  gameTitle: string;
  difficultyLevel: string;
  duration: number;
  frequency: {
    repeatEvery: string;
    periodType: string;
    timesPerWeek: string;
    daysOfWeek: string[];
    startDate: string;
    endType: 'never' | 'on' | 'after';
    endTimes?: string;
    endDate?: string;
  };
  details?: Record<string, unknown>;
  promptAi?: {
    customPrompt: string;
  };
}

export const GameSettingsModal: React.FC<GameSettingsModalProps> = ({
  isOpen,
  onClose,
  gameType,
  gameTitle,
  onSave,
  studentId = 123,
  isTestsPage = false,
}) => {
  const handleClose = () => {
    onClose();
  };
  const [activeTab, setActiveTab] = useState<TabType>('frequency');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const [settings, setSettings] = useState<GameSettings>({
    gameType,
    gameTitle,
    difficultyLevel: 'Medium',
    duration: 15,
    frequency: {
      repeatEvery: 'week',
      periodType: 'Week',
      timesPerWeek: '2x',
      daysOfWeek: ['M', 'T', 'W', 'T', 'F'],
      startDate: new Date().toISOString().split('T')[0],
      endType: 'after',
      endTimes: '12',
    },
    promptAi: {
      customPrompt: '',
    },
  });

  React.useEffect(() => {
    setSettings(prev => ({
      ...prev,
      gameType,
      gameTitle,
    }));
  }, [gameType, gameTitle]);

  const updateSettings = (section: keyof GameSettings, data: Record<string, unknown>) => {
    setSettings(prev => {
      if (section === 'frequency') {
        return {
          ...prev,
          frequency: {
            ...prev.frequency,
            ...(data as Partial<GameSettings['frequency']>),
          },
        };
      } else if (section === 'promptAi' && prev.promptAi) {
        return {
          ...prev,
          promptAi: {
            ...prev.promptAi,
            ...(data as Partial<GameSettings['promptAi']>),
          },
        };
      } else if (section === 'promptAi') {
        return {
          ...prev,
          promptAi: data as GameSettings['promptAi'],
        };
      } else {
        return {
          ...prev,
          [section]: {
            ...(prev[section] as Record<string, unknown>),
            ...data,
          },
        };
      }
    });
  };

  const handleSave = async () => {
    try {
      setIsSubmitting(true);

      if (!studentId) {
        toast.error('Student ID is required to assign intervention');
        return;
      }

      const completeSettings: GameSettings = {
        gameType,
        gameTitle,
        difficultyLevel: settings.difficultyLevel,
        duration: settings.duration,
        frequency: settings.frequency,
        promptAi: settings.promptAi,
        details: settings.details,
      };

      logger.log('Sending complete settings:', completeSettings);
      logger.log('Student ID:', studentId);

      if (onSave) {
        await onSave(completeSettings);
      }

      onClose();
    } catch (error) {
      logger.error('Error saving intervention:', error);
      toast.error('Failed to assign intervention. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'frequency':
        return (
          <FrequencyTab
            settings={settings.frequency}
            onChange={data => {
              updateSettings('frequency', data as Record<string, unknown>);
            }}
          />
        );
      case 'details':
        return <DetailsTab gameType={gameType} />;
      case 'promptAi':
        return (
          <PromptAiTab
            settings={settings.promptAi || { customPrompt: '' }}
            onChange={data => {
              if (data) {
                updateSettings('promptAi', data as Record<string, unknown>);
              }
            }}
          />
        );
      default:
        return null;
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <div className="flex flex-col h-full">
        <div
          className="flex w-full"
          style={{ paddingTop: '42px', borderBottom: '1px solid #e5e7eb' }}
        >
          <TabButton
            isActive={activeTab === 'frequency'}
            onClick={() => setActiveTab('frequency')}
            label="Frequency"
            position="left"
          />
          <TabButton
            isActive={activeTab === 'details'}
            onClick={() => setActiveTab('details')}
            label="Details"
            position="middle"
          />
          <TabButton
            isActive={activeTab === 'promptAi'}
            onClick={() => setActiveTab('promptAi')}
            label="Prompt Ai"
            position="right"
          />
        </div>

        <div className="px-10 pt-8">
          <h2 className="text-3xl font-bold mb-4">{gameTitle}</h2>
          <hr className="mb-8" />
        </div>

        <div className="px-10 flex-grow overflow-y-auto">{renderTabContent()}</div>

        <div className="flex justify-center my-8 space-x-6">
          <button
            className="flex items-center justify-center bg-[#005773] text-white transition-opacity"
            onClick={handleSave}
            disabled={isSubmitting}
            style={{
              width: '214px',
              height: '56px',
              borderRadius: '16px',
              fontSize: '16px',
              fontWeight: 500,
              opacity: isSubmitting ? 0.7 : 1,
            }}
          >
            {isSubmitting ? (
              <>
                <span className="mr-3">
                  <svg
                    className="animate-spin h-5 w-5 text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                </span>
                Processing...
              </>
            ) : (
              <>
                <span className="mr-3">
                  <img src={detailSettingsIcon} alt="Settings" width="24" height="24" />
                </span>
                {isTestsPage ? 'Add test' : 'Add intervention'}
              </>
            )}
          </button>
          <button
            className="flex items-center justify-center bg-red-500 hover:bg-red-600 text-white transition-colors"
            onClick={handleClose}
            style={{
              width: '214px',
              height: '56px',
              borderRadius: '16px',
              fontSize: '16px',
              fontWeight: 500,
              opacity: isSubmitting ? 0.7 : 1,
            }}
          >
            <svg
              className="w-5 h-5 mr-2"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M10 19l-7-7m0 0l7-7m-7 7h18"
              />
            </svg>
            Back
          </button>
        </div>
      </div>
    </Modal>
  );
};

interface TabButtonProps {
  isActive: boolean;
  onClick: () => void;
  label: string;
  position: 'left' | 'middle' | 'right';
}

const TabButton: React.FC<TabButtonProps> = ({ isActive, onClick, label, position }) => {
  return (
    <button
      className={`flex-1 py-4 px-4 text-center transition-colors border-b-2 ${
        isActive
          ? 'bg-[#005773] text-white border-[#005773]'
          : 'bg-white text-gray-700 hover:bg-gray-50 border-transparent'
      } ${position === 'left' ? 'ml-5' : ''} ${position === 'right' ? 'mr-5' : ''}`}
      onClick={onClick}
      style={{
        borderTopLeftRadius: position === 'left' ? '8px' : '0px',
        borderBottomLeftRadius: position === 'left' ? '8px' : '0px',
        borderTopRightRadius: position === 'right' ? '8px' : '0px',
        borderBottomRightRadius: position === 'right' ? '8px' : '0px',
        width: position === 'middle' ? '33.4%' : '33.3%',
        fontWeight: 500,
        fontSize: '16px',
      }}
    >
      {label}
    </button>
  );
};

interface FrequencyTabProps {
  settings: GameSettings['frequency'];
  onChange: (data: Partial<GameSettings['frequency']>) => void;
}

const FrequencyTab: React.FC<FrequencyTabProps> = ({ settings, onChange }) => {
  const handleDayToggle = (day: string) => {
    const updatedDays = settings.daysOfWeek.includes(day)
      ? settings.daysOfWeek.filter(d => d !== day)
      : [...settings.daysOfWeek, day];

    onChange({ daysOfWeek: updatedDays });
  };

  return (
    <div className="space-y-10">
      <div>
        <div className="text-lg font-medium text-gray-800 mb-4">Repeat Every</div>
        <div className="flex">
          <input
            type="text"
            value={settings.repeatEvery}
            onChange={e => onChange({ repeatEvery: e.target.value })}
            className="w-24 border border-gray-300 rounded-md p-3 mr-4 text-center"
          />
          <div className="relative flex-1">
            <select
              value={settings.periodType}
              onChange={e => onChange({ periodType: e.target.value })}
              className="appearance-none border border-gray-300 rounded-md p-3 w-full pr-10 bg-white"
            >
              <option value="Day">Day</option>
              <option value="Week">Week</option>
              <option value="Month">Month</option>
            </select>
            <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
              <svg
                className="fill-current h-4 w-4"
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 20 20"
              >
                <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
              </svg>
            </div>
          </div>
        </div>
      </div>

      <div>
        <div className="text-lg font-medium text-gray-800 mb-4">Multiple per week</div>
        <div className="flex space-x-4">
          {['1x', '2x', '3x', '4x', '5x'].map(option => (
            <button
              key={option}
              className={`w-14 h-14 rounded-md flex items-center justify-center text-lg ${
                settings.timesPerWeek === option
                  ? 'border-2 border-[#005773] text-[#005773]'
                  : 'border border-gray-300 text-gray-700'
              }`}
              onClick={() => onChange({ timesPerWeek: option })}
            >
              {option}
            </button>
          ))}
        </div>
      </div>

      <div>
        <div className="text-lg font-medium text-gray-800 mb-4">Repeat On</div>
        <div className="flex space-x-4">
          {['S', 'M', 'T', 'W', 'T', 'F', 'S'].map((day, index) => (
            <button
              key={index}
              className={`w-14 h-14 rounded-md flex items-center justify-center text-lg ${
                settings.daysOfWeek.includes(day)
                  ? 'border-2 border-[#005773] bg-[#005773] text-white'
                  : 'border border-gray-300 text-gray-700'
              }`}
              onClick={() => handleDayToggle(day)}
            >
              {day}
            </button>
          ))}
        </div>
      </div>

      <div>
        <div className="text-lg font-medium text-gray-800 mb-4">Start</div>
        <div className="relative">
          <select
            value={settings.startDate}
            onChange={e => onChange({ startDate: e.target.value })}
            className="appearance-none border border-gray-300 rounded-md p-3 w-full pr-10 bg-white"
          >
            <option value="Today">Today</option>
            <option value="Tomorrow">Tomorrow</option>
            <option value="NextWeek">Next Week</option>
          </select>
          <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
            <svg
              className="fill-current h-4 w-4"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 20 20"
            >
              <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
            </svg>
          </div>
        </div>
      </div>

      <div>
        <div className="text-lg font-medium text-gray-800 mb-4">Ends</div>
        <div className="space-y-4">
          <div className="flex items-center">
            <input
              type="radio"
              id="never"
              name="endOption"
              checked={settings.endType === 'never'}
              onChange={() => onChange({ endType: 'never' })}
              className="w-5 h-5 mr-3 text-[#005773]"
            />
            <label htmlFor="never" className="text-lg">
              Never
            </label>
          </div>
          <div className="flex items-center">
            <input
              type="radio"
              id="on"
              name="endOption"
              checked={settings.endType === 'on'}
              onChange={() => onChange({ endType: 'on' })}
              className="w-5 h-5 mr-3 text-[#005773]"
            />
            <label htmlFor="on" className="text-lg">
              On
            </label>
          </div>
          <div className="flex items-center">
            <input
              type="radio"
              id="after"
              name="endOption"
              checked={settings.endType === 'after'}
              onChange={() => onChange({ endType: 'after' })}
              className="w-5 h-5 mr-3 text-[#005773]"
            />
            <label htmlFor="after" className="text-lg">
              After
            </label>
            {settings.endType === 'after' && (
              <div className="flex items-center ml-4">
                <input
                  type="text"
                  value={settings.endTimes}
                  onChange={e => onChange({ endTimes: e.target.value })}
                  className="w-16 border border-gray-300 rounded-md p-2 mr-3 text-center"
                />
                <span className="text-lg">Times</span>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

interface DetailsTabProps {
  gameType: GameType;
}

const DetailsTab: React.FC<DetailsTabProps> = ({ gameType }) => {
  if (gameType === 'blend-game--quiz') {
    return (
      <div className="flex flex-col items-center justify-center h-full py-8">
        <div className="text-3xl text-blue-600 font-medium mb-8">
          What&apos;s the missing blend?
        </div>

        <div className="flex items-center space-x-12 mb-10">
          <div className="border-2 border-blue-300 rounded-2xl p-6">
            <img
              src="https://cdn-icons-png.flaticon.com/512/814/814513.png"
              alt="Globe"
              className="w-40 h-40 object-contain"
            />
          </div>

          <div className="flex flex-col justify-center">
            <div className="flex items-center mb-6">
              <div className="text-4xl text-blue-600">
                <span className="text-blue-300 mr-2">...</span>
                <span className="font-medium">obe</span>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              {['cl', 'pl', 'gl', 'gr'].map(option => (
                <button
                  key={option}
                  className="border-2 border-blue-300 rounded-xl py-3 px-6 text-center text-lg hover:bg-blue-50 transition-colors"
                >
                  {option}
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (gameType === 'math-game') {
    return (
      <div className="flex flex-col items-center justify-center h-full py-8">
        <div className="flex justify-center mb-8">
          <div className="flex space-x-4">
            {[1, 2, 3].map(cup => (
              <div key={cup} className="text-yellow-500">
                <svg
                  width="48"
                  height="48"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M2 8H22L20 21H4L2 8Z"
                    stroke="currentColor"
                    strokeWidth="2"
                    fill="none"
                  />
                  <path
                    d="M6 8V6C6 3.79086 7.79086 2 10 2H14C16.2091 2 18 3.79086 18 6V8"
                    stroke="currentColor"
                    strokeWidth="2"
                  />
                  <path d="M12 11V16" stroke="currentColor" strokeWidth="2" strokeLinecap="round" />
                  <path d="M8 14H16" stroke="currentColor" strokeWidth="2" strokeLinecap="round" />
                </svg>
              </div>
            ))}
          </div>
        </div>

        <div className="flex items-center justify-center space-x-4 mb-8">
          <div className="w-16 h-16 rounded-full border-2 border-gray-800 flex items-center justify-center text-2xl font-bold">
            3
          </div>
          <div className="text-2xl font-bold">-</div>
          <div className="w-16 h-16 rounded-full border-2 border-gray-800 flex items-center justify-center text-2xl font-bold">
            1
          </div>
          <div className="text-2xl font-bold">=</div>
          <div className="w-16 h-16 rounded-full border-2 border-gray-800 flex items-center justify-center text-2xl font-bold">
            ?
          </div>
        </div>

        <div className="mb-8">
          <div className="text-lg font-medium mb-2">Your answer:</div>
          <div className="relative">
            <input
              type="text"
              className="w-64 border-2 border-gray-300 rounded-lg p-3 text-center text-xl"
              placeholder="?"
            />
          </div>
        </div>

        <button className="bg-purple-400 text-white py-3 px-8 rounded-full text-lg font-medium hover:bg-purple-500 transition-colors">
          Check Answer
        </button>
      </div>
    );
  }

  if (gameType === 'memory-game') {
    return (
      <div className="flex flex-col items-center justify-center h-full py-8">
        <div className="w-full max-w-xl">
          <div className="flex justify-between items-center mb-6">
            <div className="text-lg font-medium">Moves: 0</div>
            <div className="text-lg font-medium">Pairs Found: 0 / 8</div>
          </div>

          <div className="grid grid-cols-4 gap-4">
            {Array.from({ length: 16 }).map((_, index) => (
              <div
                key={index}
                className="aspect-square bg-indigo-600 rounded-lg cursor-pointer hover:bg-indigo-700 transition-colors"
              />
            ))}
          </div>
        </div>
      </div>
    );
  }

  return <div className="flex items-center justify-center h-full">Details for {gameType}</div>;
};

interface PromptAiTabProps {
  settings: GameSettings['promptAi'];
  onChange: (data: Partial<GameSettings['promptAi']>) => void;
}

const PromptAiTab: React.FC<PromptAiTabProps> = ({ settings, onChange }) => {
  return (
    <div className="flex flex-col items-center justify-center h-full py-8">
      <div className="w-full max-w-2xl">
        <textarea
          value={settings?.customPrompt || ''}
          onChange={e => onChange({ customPrompt: e.target.value })}
          placeholder="Customise activity - write your own prompt here"
          className="w-full h-48 border-2 border-gray-200 rounded-xl p-6 text-lg resize-none focus:outline-none focus:border-[#005773] transition-colors"
        />
        <div className="flex justify-end mt-2">
          <button className="p-2 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors">
            <svg
              width="20"
              height="20"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M7 17L17 7"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                d="M7 7H17V17"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </button>
        </div>
      </div>
    </div>
  );
};

export default GameSettingsModal;
