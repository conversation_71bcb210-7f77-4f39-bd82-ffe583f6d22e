import React, { useState, useRef, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import plusIcon from '../../assets/plus.svg';

interface RecentCardProps {
  id: string | number;
  type?: 'chat' | 'empty';
  name?: string;
  avatar?: string;
  session_id?: string;
  last_message_time?: string;
}

export const RecentCard: React.FC<RecentCardProps> = ({
  id,
  type = 'chat',
  name = 'Student',
  avatar,
  session_id,
  last_message_time,
}) => {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const navigate = useNavigate();

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const cardClasses =
    'w-[212px] h-[200px] bg-white rounded-[20px] border border-[#E4E4E4] [box-shadow:0px_2px_4px_0px_#CACACA1A,0px_8px_8px_0px_#CACACA17,0px_17px_10px_0px_#CACACA0D,0px_30px_12px_0px_#CACACA03,0px_47px_13px_0px_#CACACA00] hover:shadow-lg transition-shadow';

  if (type === 'empty') {
    return (
      <div className={`${cardClasses} flex items-center justify-center cursor-pointer`}>
        <div className="w-12 h-12 rounded-full bg-primaryBtn flex items-center justify-center">
          <img src={plusIcon} alt="" />
        </div>
      </div>
    );
  }

  return (
    <div className={`${cardClasses} p-2 flex flex-col relative overflow-hidden`}>
      <div className="flex items-center justify-end absolute top-2 right-2" ref={dropdownRef}>
        <button
          className="w-8 h-8 flex items-center justify-center text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100"
          onClick={() => setIsDropdownOpen(!isDropdownOpen)}
        >
          <span className="text-xl transform rotate-90">⋮</span>
        </button>
        {isDropdownOpen && (
          <div className="absolute top-full right-0 mt-1 w-36 bg-white rounded-lg shadow-lg border border-gray-100 py-1 z-10">
            <button
              onClick={() => navigate(`/dashboard/student/${id}`, { state: { studentName: name } })}
              className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
            >
              Dashboard
            </button>
            <button
              onClick={() =>
                navigate(`/dashboard/student/${id}/chat`, { state: { studentName: name } })
              }
              className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
            >
              Chat
            </button>
            <button
              onClick={() => navigate(`/dashboard/student/${id}/report`)}
              className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
            >
              Report
            </button>
          </div>
        )}
      </div>

      <div
        className="flex-1 flex flex-col items-center justify-center cursor-pointer"
        onClick={() => {
          navigate(`/dashboard/student/${id}/chat`, { state: { studentName: name } });
        }}
      >
        {avatar ? (
          <img
            src={avatar}
            alt={name}
            className="w-[119px] h-[119px] absolute -right-5 -bottom-5 pointer-events-none -z-[1]"
          />
        ) : (
          <div className="w-16 h-16 rounded-full bg-gray-200 mb-3 flex items-center justify-center text-gray-400 text-2xl">
            {name?.[0]?.toUpperCase()}
          </div>
        )}
        <div className="text-center">
          <span className="font-inter font-semibold text-base align-middle block text-[#A4A4A4] mb-1">
            Last activity
          </span>
          {last_message_time && (
            <span className="text-xs text-gray-500 mb-2 block">
              {new Date(last_message_time).toLocaleString('pl-PL', {
                day: '2-digit',
                month: '2-digit',
                year: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
              })}
            </span>
          )}
          <h3 className="font-inter font-semibold text-[24px] text-[#1C1C1C] leading-none mb-4">
            {name}
          </h3>
        </div>
      </div>
    </div>
  );
};
