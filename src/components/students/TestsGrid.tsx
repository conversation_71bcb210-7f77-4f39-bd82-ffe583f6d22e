import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Test } from '../../mocks/mockTests';

interface TestsGridProps {
  tests: Test[];
}

export const TestsGrid: React.FC<TestsGridProps> = ({ tests }) => {
  const navigate = useNavigate();

  const handleTestClick = (test: Test) => {
    // Navigate to test page (implementation can be added later)
    navigate(`/dashboard/tests/${test.id}`);
  };

  // W testach zachowujemy biały badge jak w interwencjach, bez koloru tła
  const pillClasses =
    'absolute top-2 left-2 bg-white px-3 py-1 rounded-full text-sm font-medium whitespace-nowrap';

  const formatStatusLabel = (status: string) => {
    switch (status) {
      case 'in-progress':
        return 'In Progress';
      case 'completed':
        return 'Completed';
      default:
        return 'Not Started';
    }
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {tests.map(test => (
        <div
          key={test.id}
          className="relative bg-white rounded-xl shadow-sm overflow-hidden border hover:shadow-md transition-shadow cursor-pointer"
          onClick={() => handleTestClick(test)}
        >
          {/* Badge status w lewym górnym rogu */}
          <span className={pillClasses}>{formatStatusLabel(test.status)}</span>

          <div className="p-6 pt-14">
            <p className="text-gray-600 text-sm mb-4">{test.description}</p>

            <div className="flex items-center text-gray-500 text-sm">
              <svg
                className="w-4 h-4 mr-1"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                />
              </svg>
              {test.questions_count} questions
            </div>

            {test.completion_date && (
              <div className="flex items-center text-gray-500 text-sm mt-2">
                <svg
                  className="w-4 h-4 mr-1"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
                Completed on {new Date(test.completion_date).toLocaleDateString()}
              </div>
            )}
          </div>
        </div>
      ))}
    </div>
  );
};
