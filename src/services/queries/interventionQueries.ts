import { useMutation, useQueryClient } from '@tanstack/react-query';
import interventionService from '../interventionService';
import logger from '../../utils/logger';

// Mutation hook for assigning interventions to students
export const useAssignInterventionToStudent = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ studentId, gameType }: { studentId: number; gameType?: string }) =>
      interventionService.assignInterventionToStudent(studentId, gameType),
    onSuccess: (data, { studentId }) => {
      logger.log('Intervention assigned successfully via mutation:', data);
      // Invalidate related queries to refresh data
      queryClient.invalidateQueries({ queryKey: ['assignedInterventions', studentId] });
      queryClient.invalidateQueries({ queryKey: ['assignedInterventions'] });
    },
    onError: error => {
      logger.error('Error in assignInterventionToStudent mutation:', error);
    },
  });
};
