import axios, { AxiosResponse } from 'axios';
import cookies from '../utils/cookies';
import { clearAllStores } from '../utils/storeUtils';
import logger from '../utils/logger';

// Import API mocka
import { mockApi } from './apiMock';

// Flag to control whether to use mock API or real API.
// Controlled via VITE_USE_MOCKS env variable. Defaults to false (real API).
let useApiMock = (import.meta.env.VITE_USE_MOCKS ?? 'false') === 'true';

// Utworzenie prawdziwej instancji axios
const realApi = axios.create({
  baseURL: import.meta.env.VITE_API_URL || 'https://api.dev.yubu.ai',
  timeout: 5000, // Zmniejszamy timeout, aby szybciej wykryć problemy z API
  headers: {
    'Content-Type': 'application/json',
  },
});

// Flag to prevent multiple simultaneous refresh attempts
let isRefreshing = false;
let failedQueue: Array<{ resolve: (value?: unknown) => void; reject: (error?: unknown) => void }> =
  [];

const processQueue = (error: unknown, token: string | null = null) => {
  failedQueue.forEach(({ resolve, reject }) => {
    if (error) {
      reject(error);
    } else {
      resolve(token);
    }
  });

  failedQueue = [];
};

// Configure interceptors for real API
realApi.interceptors.request.use(
  config => {
    const token = cookies.get('access_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  error => {
    logger.error('Request error:', error);
    return Promise.reject(error);
  }
);

realApi.interceptors.response.use(
  response => response,
  async error => {
    logger.error('Response error:', error.response || error);
    const originalRequest = error.config;

    if (error.response?.status === 401 && !originalRequest._retry) {
      // Don't retry if this is already a refresh-token request
      if (originalRequest.url?.includes('/users/refresh-token')) {
        // If refresh token request fails, logout user
        cookies.remove('access_token');
        cookies.remove('refresh_token');
        clearAllStores();
        window.location.href = '/login';
        return Promise.reject(error);
      }

      if (isRefreshing) {
        // If already refreshing, queue this request
        return new Promise((resolve, reject) => {
          failedQueue.push({ resolve, reject });
        })
          .then(token => {
            originalRequest.headers.Authorization = `Bearer ${token}`;
            return realApi(originalRequest);
          })
          .catch(err => {
            return Promise.reject(err);
          });
      }

      originalRequest._retry = true;
      isRefreshing = true;

      try {
        const { authService } = await import('./authService');
        await authService.refreshToken();
        const newToken = cookies.get('access_token');

        processQueue(null, newToken);
        isRefreshing = false;

        originalRequest.headers.Authorization = `Bearer ${newToken}`;
        return realApi(originalRequest);
      } catch (_refreshError) {
        processQueue(_refreshError, null);
        isRefreshing = false;

        cookies.remove('access_token');
        cookies.remove('refresh_token');
        clearAllStores();
        window.location.href = '/login';
        return Promise.reject(_refreshError);
      }
    }

    // Dla błędów walidacji (422) zachowujemy oryginalną strukturę błędu
    if (error.response?.status === 422) {
      logger.error('Validation error details:', {
        status: error.response?.status,
        data: error.response?.data,
      });
      return Promise.reject(error);
    }

    const errorMessage = error.response?.data?.message || error.message || 'An error occurred';
    logger.error('Error details:', {
      status: error.response?.status,
      message: errorMessage,
      data: error.response?.data,
      headers: error.response?.headers,
    });

    return Promise.reject(error);
  }
);

// Funkcja do sprawdzania, czy API działa
export const checkApiHealth = async (): Promise<boolean> => {
  try {
    // Próbujemy wykonać prosty request do API (endpoint /health lub inny prosty endpoint)
    await realApi.get('/health', { timeout: 3000 });
    useApiMock = false; // API działa, używamy prawdziwego API
    logger.info('API health check: API is working, using real API');
    return true;
  } catch (error) {
    logger.warn('API health check: API is not responding', error);
    return false;
  }
};

// Initialize API configuration based on environment variable
logger.info('API Configuration:', {
  useApiMock,
  apiUrl: import.meta.env.VITE_API_URL,
  envMocks: import.meta.env.VITE_USE_MOCKS,
});

// Helper types and functions
// Allowed HTTP methods
type HttpMethod = 'get' | 'post' | 'put' | 'delete' | 'patch';

// Minimal client interface we rely on
interface HttpClient {
  get<U>(url: string, config?: unknown): Promise<AxiosResponse<U>>;
  delete<U>(url: string, config?: unknown): Promise<AxiosResponse<U>>;
  post<U>(url: string, data?: unknown, config?: unknown): Promise<AxiosResponse<U>>;
  put<U>(url: string, data?: unknown, config?: unknown): Promise<AxiosResponse<U>>;
  patch<U>(url: string, data?: unknown, config?: unknown): Promise<AxiosResponse<U>>;
}

function executeRequest<R>(
  client: HttpClient,
  method: HttpMethod,
  url: string,
  data?: unknown,
  config?: unknown
): Promise<AxiosResponse<R>> {
  switch (method) {
    case 'get':
      return client.get<R>(url, config);
    case 'delete':
      return client.delete<R>(url, config);
    case 'post':
      return client.post<R>(url, data, config);
    case 'put':
      return client.put<R>(url, data, config);
    case 'patch':
      return client.patch<R>(url, data, config);
    default:
      // Should never reach here
      return Promise.reject(new Error(`Unsupported method ${method}`));
  }
}

const makeRequest = async <T = unknown>(
  _method: HttpMethod,
  _url: string,
  _data?: unknown,
  _config?: unknown
): Promise<AxiosResponse<T>> => {
  // Log request details
  logger.info(`API request: ${_method.toUpperCase()} ${_url}`, { useApiMock, data: _data });

  // If mocks are disabled, always use real API
  if (!useApiMock) {
    const response = await executeRequest<T>(realApi, _method, _url, _data, _config);
    logger.info(`Real API response for ${_url}`, { status: response.status });
    return response;
  }

  // If mocks are enabled, use mock API
  logger.info(`Using mock API for ${_method.toUpperCase()} ${_url}`);
  return await executeRequest<T>(mockApi as unknown as HttpClient, _method, _url, _data, _config);
};

export const api = {
  get: <T = unknown>(_url: string, _config?: unknown) =>
    makeRequest<T>('get', _url, undefined, _config),
  post: <T = unknown>(_url: string, _data?: unknown, _config?: unknown) =>
    makeRequest<T>('post', _url, _data, _config),
  put: <T = unknown>(_url: string, _data?: unknown, _config?: unknown) =>
    makeRequest<T>('put', _url, _data, _config),
  delete: <T = unknown>(_url: string, _config?: unknown) =>
    makeRequest<T>('delete', _url, undefined, _config),
  patch: <T = unknown>(_url: string, _data?: unknown, _config?: unknown) =>
    makeRequest<T>('patch', _url, _data, _config),
};

// Export flag indicating whether we are using mocks
export const USE_MOCKS = () => useApiMock;
