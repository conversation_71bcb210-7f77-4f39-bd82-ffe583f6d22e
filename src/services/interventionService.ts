import { authService } from './authService';
import game1Image from '../assets/game1.png';
import game2Image from '../assets/game2.png';
import game3Image from '../assets/game3.png';
import logger from '../utils/logger';

interface InterventionAssignment {
  student_id: number;
  intervention_id: number | string;
}

export interface InterventionSettings {
  gameType: string;
  gameTitle: string;
  difficultyLevel: string;
  frequency: {
    repeatEvery: string;
    periodType: string;
    daysOfWeek?: string[];
    time?: string;
    timesPerWeek?: number;
    endType?: 'never' | 'on' | 'after';
    endDate?: string;
    endTimes?: number;
  };
  duration: number;
  studentId?: number;
  interventionId?: number | string;
}

interface SupportArea {
  id: string;
  name: string;
  description: string;
  icon?: string;
}

interface AssignedIntervention {
  id: string;
  intervention_id?: string;
  name: string;
  description: string;
  status: 'completed' | 'in-progress' | 'locked';
  progress: {
    current: number;
    total: number;
  };
  time_spent: string;
  start_date?: string;
  end_date?: string;
  repeat_on?: string[];
  repeat_every?: number;
  sessions_per_day?: number;
  assigned_by_id?: number;
  assigned_by_role?: string;
  created_at?: string;
  // Pola dodane dla widoku
  display_title: string;
  display_status: string;
  image_type: string;
}

interface Game {
  id: number;
  type: string;
  title: string;
  description: string;
  exerciseTime: string;
  imageUrl: string;
}

interface ScheduleInterventionRequest {
  student_id: number;
  intervention_id: string;
  repeat_every_value: number;
  repeat_every_unit: 'day' | 'week' | 'month';
  times_per_week: number;
  repeat_on: string[];
  start_date: string;
  ends: {
    mode: 'never' | 'on' | 'after';
    on_date?: string;
    after_times?: number;
  };
}

const API_URL = import.meta.env.VITE_API_URL || 'https://api.dev.yubu.ai';

// Funkcja pomocnicza do formatowania tytułu interwencji
const formatInterventionTitle = (title: string): string => {
  return title.replace(/-/g, ' ').replace(/_/g, ' ');
};

export const getFallbackGamesForArea = (areaId: string): Game[] => {
  const areaNames: Record<string, string> = {
    'phonemic-awareness': 'Phonemic Awareness',
    'phonics-decoding': 'Phonics & Decoding',
    'reading-fluency': 'Reading Fluency',
    'vocabulary-development': 'Vocabulary Development',
    'reading-comprehension': 'Reading Comprehension',
    'orthographic-processing': 'Orthographic Processing',
    'written-expression': 'Written Expression',
    'executive-function': 'Executive Function',
  };

  const areaName = areaNames[areaId] || 'Reading Support';

  return [
    {
      id: 1000 + Math.floor(Math.random() * 1000),
      type: 'blend',
      title: `${areaName} Activity`,
      description: `Practice ${areaName.toLowerCase()} skills with this interactive game.`,
      exerciseTime: '5 min.',
      imageUrl: game1Image,
    },
    {
      id: 2000 + Math.floor(Math.random() * 1000),
      type: 'math',
      title: `${areaName} Quiz`,
      description: `Improve ${areaName.toLowerCase()} skills with this quiz.`,
      exerciseTime: '8 min.',
      imageUrl: game2Image,
    },
    {
      id: 3000 + Math.floor(Math.random() * 1000),
      type: 'memory',
      title: `${areaName} Memory Game`,
      description: `Enhance ${areaName.toLowerCase()} skills with this memory game.`,
      exerciseTime: '10 min.',
      imageUrl: game3Image,
    },
  ];
};

const interventionService = {
  getSupportAreas: async (): Promise<SupportArea[]> => {
    try {
      const token = authService.getAccessToken();

      if (!token) {
        throw new Error('No authentication token available');
      }

      const response = await fetch(`${API_URL}/reading_areas`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error(`Error fetching support areas: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      logger.error('Error in getSupportAreas:', error);
      throw error;
    }
  },

  createInterventionFromSettings: async (
    settings: InterventionSettings & {
      frequency: {
        startDate?: string;
        repeatEvery?: string | number;
        timesPerWeek?: string | number;
        daysOfWeek?: string[];
        endType?: 'never' | 'on' | 'after';
        endDate?: string;
        endTimes?: number;
      };
    },
    studentId: number
  ): Promise<number> => {
    try {
      const token = authService.getAccessToken();

      if (!token) {
        throw new Error('No authentication token available');
      }

      const effectiveStudentId = studentId;
      logger.log('Using student ID for intervention assignment:', effectiveStudentId);

      const interventionIdForGameType = settings.gameType; // use slug/id from frontend
      logger.log(
        'Using intervention ID for game type:',
        settings.gameType,
        interventionIdForGameType
      );

      try {
        const currentUser = await authService.getCurrentUser();
        logger.log('Current user for intervention assignment:', currentUser);
      } catch (error) {
        logger.error('Error getting current user:', error);
        throw new Error('Failed to validate user session. Please log in again.');
      }

      // First assign the intervention to the student
      const assignmentData = [
        {
          student_id: Number(effectiveStudentId),
          intervention_id: String(interventionIdForGameType),
        },
      ];

      logger.log('Sending intervention assignment data:', assignmentData);

      const interventionResponse = await fetch(`${API_URL}/chat/assign_interventions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(assignmentData),
      });

      if (!interventionResponse.ok) {
        const errorData = await interventionResponse.json().catch(() => ({}));
        logger.error('Intervention assignment error details:', errorData);
        logger.error(
          'Assignment data that caused the error:',
          JSON.stringify(assignmentData, null, 2)
        );

        // Handle different error status codes
        if (interventionResponse.status === 400) {
          throw new Error(
            errorData.detail ||
              'Invalid data format for intervention assignment. Please check student ID and intervention ID.'
          );
        } else if (interventionResponse.status === 403) {
          throw new Error('You do not have permission to assign interventions.');
        } else {
          throw new Error(
            errorData.detail ||
              errorData.message ||
              `Error assigning intervention: ${interventionResponse.statusText}`
          );
        }
      }

      const responseData = await interventionResponse.json();
      logger.log('Complete response from assign_interventions:', JSON.stringify(responseData));

      const gameTypeToInterventionIdMap: Record<string, number> = {
        blend: 1, // Phonemic Awareness Activity
        math: 2, // Math Quiz
        memory: 3, // Memory Game
      };

      let interventionId = gameTypeToInterventionIdMap[settings.gameType];

      // Use intervention ID from response or default mapping
      if (!interventionId) {
        interventionId =
          responseData.intervention_id ||
          responseData.id ||
          settings.interventionId ||
          (Array.isArray(responseData) && responseData[0]?.intervention_id) ||
          1; // Default to blend game
      }

      logger.log('Using intervention ID:', interventionId);

      if (!interventionId) {
        throw new Error(
          'No intervention ID available. The backend did not return a valid intervention ID. Please contact support.'
        );
      }

      const repeatEveryValue = parseInt(settings.frequency?.repeatEvery?.toString() || '1', 10);
      const timesPerWeek = parseInt(settings.frequency?.timesPerWeek?.toString() || '5', 10);
      const startDate = settings.frequency?.startDate || new Date().toISOString().split('T')[0];

      const dayMap: Record<string, string> = {
        M: 'Monday',
        T: 'Tuesday',
        W: 'Wednesday',
        Th: 'Thursday',
        F: 'Friday',
        S: 'Saturday',
        Su: 'Sunday',
        Mon: 'Monday',
        Tue: 'Tuesday',
        Wed: 'Wednesday',
        Thu: 'Thursday',
        Fri: 'Friday',
        Sat: 'Saturday',
        Sun: 'Sunday',
      };

      const formattedDays = (settings.frequency?.daysOfWeek || []).map(day => {
        const fullDay = dayMap[day] || day;
        const formattedDay = fullDay.charAt(0).toUpperCase() + fullDay.slice(1).toLowerCase();
        const validDays = [
          'Monday',
          'Tuesday',
          'Wednesday',
          'Thursday',
          'Friday',
          'Saturday',
          'Sunday',
        ];
        if (!validDays.includes(formattedDay)) {
          logger.warn(`Invalid day of week: ${day}, defaulting to Monday`);
          return 'Monday';
        }
        return formattedDay;
      });

      const periodType =
        settings.frequency?.periodType?.toLowerCase() === 'day'
          ? 'day'
          : settings.frequency?.periodType?.toLowerCase() === 'month'
            ? 'month'
            : 'week';

      const endCondition: {
        mode: 'never' | 'on' | 'after';
        on_date?: string;
        after_times?: number;
      } = {
        mode: settings.frequency?.endType || 'never',
      };

      if (settings.frequency?.endType === 'on' && settings.frequency?.endDate) {
        endCondition.on_date = settings.frequency.endDate;
      } else if (settings.frequency?.endType === 'after' && settings.frequency?.endTimes) {
        let afterTimes: number;
        if (typeof settings.frequency.endTimes === 'number') {
          afterTimes = settings.frequency.endTimes;
        } else if (
          settings.frequency.endTimes !== null &&
          settings.frequency.endTimes !== undefined
        ) {
          afterTimes = parseInt(String(settings.frequency.endTimes), 10) || 10;
        } else {
          afterTimes = 10;
        }
        endCondition.after_times = Math.max(1, afterTimes);
      }

      const validDaysSet = new Set(
        formattedDays.filter(day =>
          ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'].includes(
            day
          )
        )
      );

      let validDays = Array.from(validDaysSet);

      if (validDays.length === 0) {
        validDays = ['Monday', 'Wednesday', 'Friday'];
        logger.warn('No valid days provided, defaulting to Monday, Wednesday, Friday');
      }

      logger.log('Using days of week:', validDays);

      let stringInterventionId: string;
      if (typeof interventionId === 'number') {
        stringInterventionId = interventionId.toString();
      } else if (typeof interventionId === 'string') {
        stringInterventionId = interventionId;
      } else if (interventionId === undefined || interventionId === null) {
        const gameTypeId =
          settings.gameType === 'blend'
            ? 1
            : settings.gameType === 'math'
              ? 2
              : settings.gameType === 'memory'
                ? 3
                : 1;
        stringInterventionId = gameTypeId.toString();
      } else {
        stringInterventionId = '1';
      }

      const scheduleData = {
        student_id: effectiveStudentId.toString(),
        intervention_id: stringInterventionId,
        repeat_every_value: Math.max(1, repeatEveryValue),
        repeat_every_unit: periodType,
        times_per_week: Math.min(40, Math.max(1, timesPerWeek)),
        repeat_on: validDays,
        start_date: startDate,
        ends: endCondition,
      };

      logger.log('Scheduling intervention with data:', scheduleData);

      try {
        // Informacje o harmonogramie dla logowania
        const _schedulingInfo = {
          timestamp: new Date().toISOString(),
          student_id: scheduleData.student_id,
          intervention_id: scheduleData.intervention_id,
          schedule: {
            repeat_every_value: scheduleData.repeat_every_value,
            repeat_every_unit: scheduleData.repeat_every_unit,
            times_per_week: scheduleData.times_per_week,
            repeat_on: scheduleData.repeat_on,
            start_date: scheduleData.start_date,
            ends: scheduleData.ends,
          },
        };

        // Wyślij harmonogram do backendu
        try {
          const scheduleResp = await interventionService.scheduleIntervention({
            student_id: Number(scheduleData.student_id),
            intervention_id: scheduleData.intervention_id,
            repeat_every_value: scheduleData.repeat_every_value,
            repeat_every_unit: scheduleData.repeat_every_unit as 'day' | 'week' | 'month',
            times_per_week: scheduleData.times_per_week,
            repeat_on: scheduleData.repeat_on,
            start_date: scheduleData.start_date,
            ends: scheduleData.ends,
          });
          logger.log('schedule_intervention response:', scheduleResp);
        } catch (schedErr) {
          logger.error('scheduleIntervention failed:', schedErr);
          throw schedErr;
        }

        return interventionId;
      } catch (storageError) {
        logger.error('Błąd podczas zapisywania danych planowania lokalnie:', storageError);
      }

      logger.log(
        'Dane planowania, które zostałyby wysłane do backendu:',
        JSON.stringify(scheduleData, null, 2)
      );

      return interventionId;
    } catch (error) {
      logger.error('Error in createInterventionFromSettings:', error);
      throw error;
    }
  },

  scheduleIntervention: async (
    scheduleData: ScheduleInterventionRequest
  ): Promise<{ detail: string }> => {
    try {
      const token = authService.getAccessToken();
      logger.log('Token available:', !!token);

      if (!token) {
        throw new Error('No authentication token available');
      }

      logger.log('Scheduling intervention:', scheduleData);

      const repeatOn = scheduleData.repeat_on?.map(day => {
        return day.charAt(0).toUpperCase() + day.slice(1).toLowerCase();
      }) || ['Monday', 'Wednesday', 'Friday'];

      let endsObject: { mode: 'never' | 'on' | 'after'; on_date?: string; after_times?: number };

      if (!scheduleData.ends || !scheduleData.ends.mode) {
        endsObject = { mode: 'never' };
      } else {
        const mode = scheduleData.ends.mode as 'never' | 'on' | 'after';

        if (mode === 'never') {
          endsObject = { mode: 'never' };
        } else if (mode === 'on') {
          endsObject = {
            mode: 'on',
            on_date: scheduleData.ends.on_date || new Date().toISOString().split('T')[0],
          };
        } else if (mode === 'after') {
          let afterTimes: number;
          if (typeof scheduleData.ends.after_times === 'number') {
            afterTimes = scheduleData.ends.after_times;
          } else if (scheduleData.ends.after_times) {
            afterTimes = parseInt(String(scheduleData.ends.after_times), 10);
            if (isNaN(afterTimes)) afterTimes = 10;
          } else {
            afterTimes = 10;
          }

          endsObject = {
            mode: 'after',
            after_times: afterTimes,
          };
        } else {
          endsObject = { mode: 'never' };
        }
      }

      const apiData = {
        student_id: scheduleData.student_id,
        intervention_id: scheduleData.intervention_id,
        repeat_every_value: scheduleData.repeat_every_value || 1,
        repeat_every_unit: scheduleData.repeat_every_unit || 'week',
        times_per_week: scheduleData.times_per_week || 2,
        repeat_on: repeatOn,
        start_date: scheduleData.start_date || new Date().toISOString().split('T')[0],
        ends: endsObject,
        sessions_per_day: 1, // Add required field
      };

      logger.log('Formatted API data:', JSON.stringify(apiData, null, 2));

      const response = await fetch(`${API_URL}/games/schedule_intervention`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(apiData),
      });

      if (!response.ok) {
        let errorDetails = '';
        try {
          const errorResponse = await response.json();
          errorDetails = JSON.stringify(errorResponse);
        } catch {
          errorDetails = response.statusText;
        }

        throw new Error(`Error scheduling intervention: ${errorDetails}`);
      }

      return await response.json();
    } catch (error) {
      logger.error('Error in scheduleIntervention:', error);
      throw error;
    }
  },

  convertSettingsToSchedule: (
    settings: InterventionSettings,
    studentId: number,
    interventionId: number | string
  ): ScheduleInterventionRequest => {
    logger.log('Converting settings to schedule:', settings);

    const today = new Date();
    const formattedDate = today.toISOString().split('T')[0];

    const validDaysOfWeek = [
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday',
      'Sunday',
    ];

    let daysOfWeek = settings.frequency?.daysOfWeek || ['Monday', 'Wednesday', 'Friday'];

    daysOfWeek = daysOfWeek.map(day => {
      return day.charAt(0).toUpperCase() + day.slice(1).toLowerCase();
    });

    daysOfWeek = daysOfWeek.filter(day => validDaysOfWeek.includes(day));
    if (daysOfWeek.length === 0) {
      daysOfWeek = ['Monday', 'Wednesday', 'Friday'];
    }

    let timesPerWeek = 1;
    if (settings.frequency?.timesPerWeek !== undefined) {
      if (typeof settings.frequency.timesPerWeek === 'number') {
        timesPerWeek = Math.min(Math.max(1, settings.frequency.timesPerWeek), 40);
      } else if (typeof settings.frequency.timesPerWeek === 'string') {
        const timesPerWeekStr = String(settings.frequency.timesPerWeek);
        const numericValue = timesPerWeekStr.replace(/[^0-9]/g, '');
        if (numericValue) {
          timesPerWeek = Math.min(Math.max(1, parseInt(numericValue, 10)), 40);
        }
      }
    }

    const validUnits = ['day', 'week', 'month'];
    let repeatEveryUnit: 'day' | 'week' | 'month' = 'week';

    if (settings.frequency?.periodType) {
      const lowerCaseUnit = settings.frequency.periodType.toLowerCase();
      if (validUnits.includes(lowerCaseUnit)) {
        repeatEveryUnit = lowerCaseUnit as 'day' | 'week' | 'month';
      }
    }

    // Keep intervention_id as string if it's already a string (like 'math-game')
    // Only convert to number if it's actually a number
    const finalInterventionId =
      typeof interventionId === 'string'
        ? isNaN(parseInt(interventionId, 10))
          ? interventionId
          : parseInt(interventionId, 10)
        : interventionId;

    const endType = (settings.frequency?.endType || 'never') as 'never' | 'on' | 'after';

    let endsObject: { mode: 'never' | 'on' | 'after'; on_date?: string; after_times?: number } = {
      mode: 'never',
    };

    if (endType === 'on' && settings.frequency?.endDate) {
      endsObject = {
        mode: 'on',
        on_date: settings.frequency.endDate,
      };
    } else if (endType === 'after') {
      let afterTimes = 10;

      if (settings.frequency?.endTimes !== undefined) {
        if (typeof settings.frequency.endTimes === 'number') {
          afterTimes = Math.max(1, settings.frequency.endTimes);
        } else if (typeof settings.frequency.endTimes === 'string') {
          const endTimesStr = String(settings.frequency.endTimes);
          const numericValue = endTimesStr.replace(/[^0-9]/g, '');
          if (numericValue) {
            afterTimes = Math.max(1, parseInt(numericValue, 10));
          }
        }
      }

      endsObject = {
        mode: 'after',
        after_times: afterTimes,
      };
    }

    logger.log('Ends object:', endsObject);

    const scheduleData = {
      student_id: studentId,
      intervention_id: String(finalInterventionId),
      repeat_every_value: 1,
      repeat_every_unit: repeatEveryUnit,
      times_per_week: timesPerWeek,
      repeat_on: daysOfWeek,
      start_date: formattedDate,
      ends: endsObject,
    };

    logger.log('Final schedule data:', scheduleData);
    return scheduleData;
  },

  createAndAssignIntervention: async (
    settings: InterventionSettings & {
      frequency: {
        startDate?: string;
        repeatEvery?: string | number;
        timesPerWeek?: string | number;
        daysOfWeek?: string[];
        endType?: 'never' | 'on' | 'after';
        endDate?: string;
        endTimes?: number;
      };
    },
    studentId: number
  ): Promise<{ interventionId: number; detail: string }> => {
    try {
      logger.log('Creating intervention with settings:', settings);

      const interventionId = await interventionService.createInterventionFromSettings(
        settings,
        studentId
      );

      logger.log(`Created intervention with ID: ${interventionId}`);

      // Assignment is handled by scheduleIntervention in createInterventionFromSettings
      logger.log('Intervention created and scheduled successfully');

      return {
        interventionId,
        detail: 'Intervention created and assigned successfully',
      };
    } catch (error) {
      interface ErrorDetails {
        name?: string;
        message: string;
        stack?: string;
        [key: string]: unknown;
      }

      let errorMessage = 'Unknown error creating intervention';
      let errorDetails: ErrorDetails = { message: errorMessage };

      if (error instanceof Error) {
        errorMessage = error.message;
        errorDetails = {
          name: error.name,
          message: error.message,
          stack: error.stack,
          ...Object.fromEntries(
            Object.entries(error).filter(
              ([key]) => key !== 'name' && key !== 'message' && key !== 'stack'
            )
          ),
        } as ErrorDetails;
      } else if (typeof error === 'object' && error !== null) {
        errorMessage = JSON.stringify(error);
        errorDetails = {
          message: errorMessage,
          ...Object.fromEntries(Object.entries(error).filter(([key]) => key !== 'message')),
        } as ErrorDetails;
      }

      logger.error('Error in createAndAssignIntervention:', {
        message: errorMessage,
        details: errorDetails,
        stack: error instanceof Error ? error.stack : undefined,
      });

      throw new Error(`Failed to create intervention: ${errorMessage}`);
    }
  },

  scheduleDetailedIntervention: async (
    scheduleData: ScheduleInterventionRequest
  ): Promise<{ detail: string }> => {
    try {
      const token = authService.getAccessToken();

      if (!token) {
        throw new Error('No authentication token available');
      }

      const response = await fetch(`${API_URL}/games/schedule_intervention`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(scheduleData),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || 'Failed to schedule intervention');
      }

      return await response.json();
    } catch (error) {
      logger.error('Error in scheduleDetailedIntervention:', error);
      throw error;
    }
  },

  getAssignedInterventions: async (studentId: number): Promise<AssignedIntervention[]> => {
    try {
      const token = authService.getAccessToken();

      if (!token) {
        throw new Error('No authentication token available');
      }

      logger.log(
        'Fetching assigned interventions using chat/view_assigned_interventions for student:',
        studentId
      );

      const response = await fetch(`${API_URL}/chat/view_assigned_interventions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({ student_id: studentId }),
      });

      if (!response.ok) {
        throw new Error(`Error fetching assigned interventions: ${response.statusText}`);
      }

      const data = await response.json();
      logger.info('API response from chat/view_assigned_interventions received:', data);

      // Sprawdzamy, czy dane są w formacie { student_id, interventions } czy { assigned_interventions }
      const interventions = data.interventions || data.assigned_interventions || [];

      if (interventions.length === 0) {
        logger.warn('No interventions found in API response', { data });
      }

      // Get additional progress data for each intervention, if available
      const now = new Date();

      // Definiujemy typ dla danych interwencji zwracanych przez API
      interface ApiIntervention {
        id: string;
        intervention_id?: string;
        name?: string;
        description?: string;
        status?: 'completed' | 'in-progress' | 'locked';
        progress?: { current: number; total: number };
        time_spent?: string;
        start_date?: string;
        end_date?: string;
        repeat_on?: string[];
        repeat_every?: number;
        sessions_per_day?: number;
        assigned_by_id?: number;
        assigned_by_role?: string;
        created_at?: string;
      }

      return interventions.map((intervention: ApiIntervention) => {
        // Create name based on intervention_id if name is not available
        const name = intervention.name || intervention.intervention_id || 'Unknown Intervention';

        // Formatujemy tytuł do wyświetlenia
        const displayTitle = formatInterventionTitle(name);

        // Określamy typ interwencji dla obrazka
        const imageType = intervention.intervention_id || intervention.id || 'default';

        // Określamy status na podstawie dostępnych danych
        const startDate =
          intervention.start_date || new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000); // Tydzień temu
        const endDate = intervention.end_date;

        let status: 'completed' | 'in-progress' | 'locked';
        if (intervention.status) {
          status = intervention.status;
        } else if (endDate && new Date(endDate) < now) {
          status = 'completed';
        } else if (new Date(startDate) <= now) {
          status = 'in-progress';
        } else {
          status = 'locked';
        }

        // Formatujemy status do wyświetlenia
        const displayStatus =
          status === 'in-progress'
            ? 'In Progress'
            : status === 'completed'
              ? 'Completed'
              : 'Locked';

        // Określamy postęp na podstawie statusu
        let progress: { current: number; total: number };
        if (intervention.progress) {
          progress = intervention.progress;
        } else {
          switch (status) {
            case 'completed': {
              progress = { current: 6, total: 6 };
              break;
            }
            case 'in-progress': {
              // Obliczamy postęp na podstawie czasu
              const totalDuration = endDate
                ? new Date(endDate).getTime() - new Date(startDate).getTime()
                : 14 * 24 * 60 * 60 * 1000; // 2 tygodnie domyślnie
              const elapsedDuration = now.getTime() - new Date(startDate).getTime();
              const progressRatio = Math.max(0, Math.min(1, elapsedDuration / totalDuration));
              progress = { current: Math.round(progressRatio * 6), total: 6 };
              break;
            }
            case 'locked': {
              progress = { current: 0, total: 6 };
              break;
            }
            default: {
              progress = { current: 0, total: 6 };
            }
          }
        }

        // Określamy czas trwania
        let timeSpent = intervention.time_spent;
        if (!timeSpent) {
          timeSpent = status === 'completed' ? '5' : '∞';
        }

        // Create description if not available
        const description =
          intervention.description || `Exercise ${displayTitle} for student skill development.`;

        return {
          id: intervention.id || intervention.intervention_id,
          intervention_id: intervention.intervention_id,
          name,
          description,
          status,
          progress,
          time_spent: timeSpent,
          start_date: intervention.start_date,
          end_date: intervention.end_date,
          repeat_on: intervention.repeat_on,
          repeat_every: intervention.repeat_every,
          sessions_per_day: intervention.sessions_per_day,
          assigned_by_id: intervention.assigned_by_id,
          assigned_by_role: intervention.assigned_by_role,
          created_at: intervention.created_at,
          // Pola dodane dla widoku
          display_title: displayTitle,
          display_status: displayStatus,
          image_type: imageType,
        };
      });
    } catch (error) {
      logger.error('Error fetching assigned interventions:', error);
      throw error;
    }
  },
};

// Eksportujemy interventionService jako domyślny eksport dla kompatybilności
export default interventionService;
